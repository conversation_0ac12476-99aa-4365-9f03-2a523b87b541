/*
 * Этот файл использует директивы Tailwind CSS v4, такие как @theme и @apply.
 * Они могут отображаться как ошибки в редакторе, но будут правильно обработаны при сборке.
 */
@import "tailwindcss";

/* Переменные темы для shadcn/ui */
:root {
  --background: var(--color-gray-50);
  --foreground: var(--color-gray-900);

  --card: var(--color-white, white);
  --card-foreground: var(--color-gray-900);

  --popover: var(--color-white, white);
  --popover-foreground: var(--color-gray-900);

  --primary: var(--color-primary-600);
  --primary-foreground: var(--color-white, white);

  --secondary: var(--color-secondary-600);
  --secondary-foreground: var(--color-white, white);

  --muted: var(--color-gray-100);
  --muted-foreground: var(--color-gray-500);

  --accent: var(--color-gray-100);
  --accent-foreground: var(--color-gray-900);

  --destructive: var(--color-error);
  --destructive-foreground: var(--color-white, white);

  --border: var(--color-gray-200);
  --input: var(--color-gray-200);
  --ring: var(--color-primary-500);

  --radius: 0.5rem;
}

.dark {
  --background: var(--color-gray-900);
  --foreground: var(--color-gray-50);

  --card: var(--color-gray-800);
  --card-foreground: var(--color-gray-50);

  --popover: var(--color-gray-800);
  --popover-foreground: var(--color-gray-50);

  --primary: var(--color-primary-500);
  --primary-foreground: var(--color-gray-50);

  --secondary: var(--color-secondary-500);
  --secondary-foreground: var(--color-gray-50);

  --muted: var(--color-gray-800);
  --muted-foreground: var(--color-gray-400);

  --accent: var(--color-gray-800);
  --accent-foreground: var(--color-gray-50);

  --destructive: var(--color-error-dark);
  --destructive-foreground: var(--color-gray-50);

  --border: var(--color-gray-700);
  --input: var(--color-gray-700);
  --ring: var(--color-primary-700);
}

@theme inline {
  /* Основные цвета */
  --color-primary-50: oklch(0.99 0.01 240);
  --color-primary-100: oklch(0.97 0.03 240);
  --color-primary-200: oklch(0.94 0.05 240);
  --color-primary-300: oklch(0.89 0.07 240);
  --color-primary-400: oklch(0.82 0.10 240);
  --color-primary-500: oklch(0.75 0.13 240);
  --color-primary-600: oklch(0.68 0.15 240);
  --color-primary-700: oklch(0.61 0.17 240);
  --color-primary-800: oklch(0.54 0.15 240);
  --color-primary-900: oklch(0.47 0.13 240);
  --color-primary-950: oklch(0.40 0.11 240);

  /* Вторичные цвета */
  --color-secondary-50: oklch(0.98 0.01 280);
  --color-secondary-100: oklch(0.96 0.03 280);
  --color-secondary-200: oklch(0.93 0.05 280);
  --color-secondary-300: oklch(0.88 0.08 280);
  --color-secondary-400: oklch(0.81 0.12 280);
  --color-secondary-500: oklch(0.74 0.15 280);
  --color-secondary-600: oklch(0.67 0.17 280);
  --color-secondary-700: oklch(0.60 0.16 280);
  --color-secondary-800: oklch(0.53 0.14 280);
  --color-secondary-900: oklch(0.46 0.12 280);
  --color-secondary-950: oklch(0.39 0.10 280);

  /* Нейтральные цвета */
  --color-gray-50: oklch(0.99 0.00 0);
  --color-gray-100: oklch(0.96 0.00 0);
  --color-gray-200: oklch(0.92 0.00 0);
  --color-gray-300: oklch(0.85 0.00 0);
  --color-gray-400: oklch(0.75 0.00 0);
  --color-gray-500: oklch(0.65 0.00 0);
  --color-gray-600: oklch(0.55 0.00 0);
  --color-gray-700: oklch(0.45 0.00 0);
  --color-gray-800: oklch(0.35 0.00 0);
  --color-gray-900: oklch(0.25 0.00 0);
  --color-gray-950: oklch(0.15 0.00 0);

  /* Акцентные цвета */
  --color-success: oklch(0.70 0.15 160);
  --color-success-dark: oklch(0.60 0.13 160);
  --color-error: oklch(0.70 0.15 30);
  --color-error-dark: oklch(0.60 0.13 30);
  --color-warning: oklch(0.75 0.15 80);
  --color-warning-dark: oklch(0.65 0.13 80);
  --color-info: oklch(0.75 0.13 240);
  --color-info-dark: oklch(0.65 0.15 240);

  /* Типографика */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Тени */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;

  /* Размеры шрифтов */
  --font-size-adaptive-xl: clamp(1.5rem, 3vw, 2rem);

  /* Настройки темы */
  --dark-mode: class; /* Используем класс 'dark' для темной темы */
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slide-in {
  0% { transform: translateY(20px); }
  100% { transform: translateY(0); }
}

@keyframes pulse-green {
  0% { box-shadow: 0 0 0 0 rgba(22, 163, 74, 0.7); }
  70% { box-shadow: 0 0 0 0.5em rgba(22, 163, 74, 0); }
  100% { box-shadow: 0 0 0 0 rgba(22, 163, 74, 0); }
}

@keyframes pulse-red {
  0% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7); }
  70% { box-shadow: 0 0 0 0.5em rgba(220, 38, 38, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0); }
}

@keyframes pop-up {
  0% { transform: scale(0.95); opacity: 0.7; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 0.9; }
}

@layer base {
  /* Базовые стили для светлой и темной тем */
  body {
    @apply bg-white text-gray-900 transition-colors duration-300;
  }

  .dark body {
    @apply bg-gray-900 text-gray-100;
  }

  /* Стили для заголовков */
  h1 {
    @apply text-3xl font-bold mb-4;
  }

  h2 {
    @apply text-2xl font-semibold mb-3;
  }

  h3 {
    @apply text-xl font-semibold mb-2;
  }

  h4 {
    @apply text-lg font-medium mb-2;
  }

  /* Стили для анимаций */
  @keyframes flash-red {
    0%, 100% { background-color: inherit; }
    50% { background-color: var(--color-error); }
  }

  @keyframes flash-green {
    0%, 100% { background-color: inherit; }
    50% { background-color: var(--color-success); }
  }

  .animate-flash-red {
    animation: flash-red 0.5s;
  }

  .animate-flash-green {
    animation: flash-green 0.5s;
  }

  /* Стили для форм треугольников */
  .clip-path-triangle-up {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border-radius: 0.375rem;
  }

  .clip-path-triangle-down {
    clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
    border-radius: 0.375rem;
  }
}
