import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Card, CardContent } from '../shared/ui/card';
import { Button } from '../shared/ui/button';
import { Bar<PERSON>hart2, Play, Pause, ArrowLeft, ArrowRight, ArrowDown, X, Trash2, Volume2, VolumeX } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import BackButton from '../shared/ui/back-button';

interface Game {
  id: number;
  correctScore: number;
  incorrectScore: number;
  duration: number;
}

interface State {
  games: Game[];
  currentGame: Game | null;
  running: number;
  elapsedTime: number;
  historyOpen: number;
  animations: { correct: number; incorrect: number };
  soundEnabled: number;
}

const ScoreCounter: React.FC = () => {
  const [state, setState] = useState<State>({
    games: [],
    currentGame: null,
    running: 0,
    elapsedTime: 0,
    historyOpen: 0,
    animations: { correct: 0, incorrect: 0 },
    soundEnabled: 1
  });

  const audioContext = useRef<AudioContext | null>(null);

  const updateState = useCallback((newState: Partial<State>) =>
    setState(prevState => ({...prevState, ...newState})), []);

  const toggleGame = useCallback(() => {
    const { running, currentGame, games, elapsedTime } = state;

    if (running) {
      if (currentGame && (currentGame.correctScore > 0 || currentGame.incorrectScore > 0)) {
        updateState({
          games: [...games, {...currentGame, duration: elapsedTime}],
          currentGame: null,
          running: 0
        });
      } else {
        updateState({
          currentGame: null,
          running: 0
        });
      }
    } else {
      updateState({
        currentGame: {
          id: games.length + 1,
          correctScore: 0,
          incorrectScore: 0,
          duration: 0
        },
        elapsedTime: 0,
        running: 1
      });
    }
    playSound(440, 0.1, 0.05);
  }, [state, updateState]);

  const updateScore = useCallback((type: 'correct' | 'incorrect', value: number) => {
    if (state.currentGame && state.running) {
      updateState({
        currentGame: {
          ...state.currentGame,
          [`${type}Score`]: Math.max(0, state.currentGame[`${type}Score`] + value)
        },
        animations: {
          ...state.animations,
          [type]: 1
        }
      });
      playSound(type === 'correct' ? 660 : 330, 0.1, 0.05);
    }
  }, [state, updateState]);

  const resetScore = useCallback(() => {
    if (state.currentGame && state.running) {
      updateState({
        currentGame: {
          ...state.currentGame,
          correctScore: 0,
          incorrectScore: 0
        }
      });
      playSound(220, 0.1, 0.05);
    }
  }, [state, updateState]);

  const playSound = (frequency: number, duration: number, volume: number) => {
    if (!audioContext.current) {
      audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }

    if (!state.soundEnabled) return;

    const oscillator = audioContext.current.createOscillator();
    const gainNode = audioContext.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.current.destination);

    oscillator.frequency.value = frequency;
    gainNode.gain.setValueAtTime(volume, audioContext.current.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.current.currentTime + duration);

    oscillator.start();
    oscillator.stop(audioContext.current.currentTime + duration);
  };

  // Таймер для отсчета времени
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (state.running) {
      interval = setInterval(() => updateState({ elapsedTime: state.elapsedTime + 1 }), 1000);
    }

    return () => clearInterval(interval);
  }, [state.running, state.elapsedTime, updateState]);

  // Обработка клавиатурных событий
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const keyActions: Record<string, (e?: KeyboardEvent) => void> = {
        'ArrowLeft': () => updateScore('correct', 1),
        'ArrowRight': () => updateScore('incorrect', 1),
        'ArrowDown': resetScore,
        ' ': (e) => {
          e?.preventDefault();
          toggleGame();
        }
      };

      if (keyActions[event.key]) {
        keyActions[event.key](event);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [updateScore, resetScore, toggleGame]);

  // Сброс анимаций
  useEffect(() => {
    ['correct', 'incorrect'].forEach(type => {
      if (state.animations[type as 'correct' | 'incorrect']) {
        setTimeout(() => updateState({
          animations: {
            ...state.animations,
            [type]: 0
          }
        }), 300);
      }
    });
  }, [state.animations, updateState]);

  // Форматирование времени
  const formatTime = useCallback((seconds: number): string =>
    `${Math.floor(seconds / 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`,
  []);

  // Расчет процентов
  const calculatePercent = useCallback((value: number, total: number): number =>
    total ? Math.round(value / total * 100) : 0,
  []);

  // Расчет текущих показателей
  const { correctPercent, incorrectPercent } = useMemo(() => {
    const totalScore = state.currentGame ? (state.currentGame.correctScore + state.currentGame.incorrectScore) : 0;
    return {
      correctPercent: calculatePercent(state.currentGame?.correctScore || 0, totalScore),
      incorrectPercent: calculatePercent(state.currentGame?.incorrectScore || 0, totalScore)
    };
  }, [state.currentGame, calculatePercent]);

  // Расчет общей статистики
  const stats = useMemo(() => {
    const correctTotal = state.games.reduce((sum, game) => sum + game.correctScore, 0);
    const incorrectTotal = state.games.reduce((sum, game) => sum + game.incorrectScore, 0);
    const totalAll = correctTotal + incorrectTotal;
    const durationTotal = state.games.reduce((sum, game) => sum + game.duration, 0);

    return {
      correctTotal,
      incorrectTotal,
      correctPercent: calculatePercent(correctTotal, totalAll),
      incorrectPercent: calculatePercent(incorrectTotal, totalAll),
      durationTotal,
      gamesCount: state.games.length
    };
  }, [state.games, calculatePercent]);

  // Определение стилей с использованием Tailwind
  const themeStyles = {
    background: 'bg-gray-50 dark:bg-gray-900',
    card: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md',
    text: 'text-gray-900 dark:text-gray-100',
    button: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100',
    scoreText: (type: string) => type === 'correct' ? 'text-green-600 dark:text-green-500' : 'text-red-600 dark:text-red-500',
    secondaryText: 'text-gray-600 dark:text-gray-400'
  };

  // Компонент модального окна статистики
  const StatsModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 overflow-y-auto z-50">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 md:p-6 w-full max-w-md md:max-w-2xl">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className={`text-2xl md:text-3xl font-bold ${themeStyles.text}`}>Статистика игр</h2>
          <Button
            onClick={() => {
              updateState({historyOpen: 0});
              playSound(440, 0.1, 0.05);
            }}
            className={`${themeStyles.button} p-2 rounded-full`}
          >
            <X size={24}/>
          </Button>
        </div>

        <div className="mb-6 md:mb-8 h-64 md:h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={state.games.map(game => ({
              name: `Игра ${game.id}`,
              correct: game.correctScore,
              incorrect: game.incorrectScore
            }))}>
              <XAxis dataKey="name"/>
              <YAxis/>
              <Tooltip/>
              <Bar dataKey="correct" fill="#10B981" name="Верно"/>
              <Bar dataKey="incorrect" fill="#EF4444" name="Неверно"/>
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-1 gap-4 md:gap-6 mb-6 md:mb-8">
          <div>
            <h3 className={`text-xl md:text-2xl font-semibold ${themeStyles.text} mb-2 md:mb-3`}>Детальная статистика</h3>
            <ul className={`${themeStyles.text} space-y-1 md:space-y-2 text-sm md:text-base`}>
              {state.games.map(game => {
                const total = game.correctScore + game.incorrectScore;
                const correctPct = calculatePercent(game.correctScore, total);
                const incorrectPct = calculatePercent(game.incorrectScore, total);

                return (
                  <li key={game.id} className="flex justify-between items-center">
                    <span>{game.correctScore > game.incorrectScore ? '✅' : '❌'} Игра {game.id}:</span>
                    <span className="flex-grow text-center">
                      <span className="text-green-600 dark:text-green-500">{game.correctScore}</span>:
                      <span className="text-red-600 dark:text-red-500">{game.incorrectScore}</span>
                    </span>
                    <span className="flex-grow text-center">
                      <span className="text-green-600 dark:text-green-500">{correctPct}%</span>:
                      <span className="text-red-600 dark:text-red-500">{incorrectPct}%</span>
                    </span>
                    <span>{formatTime(game.duration)}</span>
                  </li>
                );
              })}
            </ul>
          </div>

          <div>
            <h3 className={`text-xl md:text-2xl font-semibold ${themeStyles.text} mb-2 md:mb-3`}>Общая статистика</h3>
            <div className={`${themeStyles.text} space-y-1 md:space-y-2 text-sm md:text-base`}>
              <p>Всего игр: {stats.gamesCount}</p>
              <p>Общий счет: <span className="text-green-600 dark:text-green-500">{stats.correctTotal}</span>:<span className="text-red-600 dark:text-red-500">{stats.incorrectTotal}</span></p>
              <p>Соотношение: <span className="text-green-600 dark:text-green-500">{stats.correctPercent}%</span>:<span className="text-red-600 dark:text-red-500">{stats.incorrectPercent}%</span></p>
              <p>Общее время: {formatTime(stats.durationTotal)}</p>
              <p>Среднее время: {formatTime(stats.gamesCount ? Math.round(stats.durationTotal / stats.gamesCount) : 0)}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-between">
          <Button
            onClick={() => {
              updateState({historyOpen: 0});
              playSound(440, 0.1, 0.05);
            }}
            className={`${themeStyles.button} px-4 py-2 text-sm md:text-base font-medium rounded-lg`}
          >
            Закрыть
          </Button>
          <Button
            onClick={() => {
              updateState({games: []});
              playSound(220, 0.1, 0.05);
            }}
            className="px-4 py-2 text-sm md:text-base font-medium rounded-lg flex items-center bg-red-600 hover:bg-red-700 text-white"
          >
            <Trash2 size={20} className="mr-2"/>Сбросить статистику
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`flex flex-col items-center justify-center min-h-screen p-4 md:p-6 transition-colors duration-300 ${themeStyles.background} font-sans`}>
      <style>
        {`
        @keyframes pulse-correct {
          0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7) }
          70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0) }
          100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0) }
        }

        @keyframes pulse-incorrect {
          0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7) }
          70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0) }
          100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0) }
        }

        @keyframes pop {
          0% { transform: scale(0.95); opacity: 0.7 }
          50% { transform: scale(1.05); opacity: 1 }
          100% { transform: scale(1); opacity: 1 }
        }

        .pulse-correct {
          animation: pulse-correct 0.5s;
        }

        .pulse-incorrect {
          animation: pulse-incorrect 0.5s;
        }

        .pop {
          animation: pop 0.3s ease-out;
        }
        `}
      </style>

      <div className="mb-6 md:mb-8">
        <BackButton className="absolute top-4 left-16" />
      </div>

      <Card className={`${themeStyles.card} p-6 md:p-8 rounded-2xl w-full max-w-md transition-colors duration-300`}>
        <CardContent className="p-0">
          <div className="flex justify-between mb-6 md:mb-8">
            <Button
              onClick={() => {
                updateState({historyOpen: 1});
                playSound(440, 0.1, 0.05);
              }}
              className={`${themeStyles.button} p-2 md:p-3 rounded-full`}
            >
              <BarChart2 size={24}/>
            </Button>

            <Button
              onClick={() => {
                updateState({soundEnabled: state.soundEnabled ? 0 : 1});
                playSound(440, 0.1, 0.05);
              }}
              className={`${themeStyles.button} p-2 md:p-3 rounded-full`}
            >
              {state.soundEnabled ? <Volume2 size={24}/> : <VolumeX size={24}/>}
            </Button>
          </div>

          <h2 className={`text-2xl md:text-3xl font-bold ${themeStyles.text} text-center mb-4 md:mb-6`}>
            {state.running ? `Игра ${state.currentGame?.id}` : 'Новая игра'}
          </h2>

          <div className={`text-xl md:text-2xl font-semibold ${themeStyles.text} text-center mb-6 md:mb-8`}>
            {formatTime(state.elapsedTime)}
          </div>

          <div className="flex justify-between items-center mb-8 md:mb-10">
            <div className="flex flex-col items-center w-2/5">
              <div className="h-32 md:h-40 w-full flex items-center justify-center">
                <span
                  className={`text-8xl md:text-9xl font-bold ${themeStyles.scoreText('correct')} ${state.animations.correct ? 'pulse-correct pop' : ''}`}
                >
                  {state.currentGame?.correctScore || 0}
                </span>
              </div>
              <span className={`text-xs md:text-sm ${themeStyles.secondaryText} mt-2`}>
                {correctPercent}%
              </span>
            </div>

            <span className={`text-6xl md:text-7xl font-bold ${themeStyles.text} w-1/5 text-center`}>:</span>

            <div className="flex flex-col items-center w-2/5">
              <div className="h-32 md:h-40 w-full flex items-center justify-center">
                <span
                  className={`text-8xl md:text-9xl font-bold ${themeStyles.scoreText('incorrect')} ${state.animations.incorrect ? 'pulse-incorrect pop' : ''}`}
                >
                  {state.currentGame?.incorrectScore || 0}
                </span>
              </div>
              <span className={`text-xs md:text-sm ${themeStyles.secondaryText} mt-2`}>
                {incorrectPercent}%
              </span>
            </div>
          </div>

          <Button
            onClick={toggleGame}
            className={`w-full py-4 md:py-5 text-xl md:text-2xl font-medium flex items-center justify-center mb-6 md:mb-8 rounded-xl ${
              state.running
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {state.running ? <Pause size={28} className="mr-3"/> : <Play size={28} className="mr-3"/>}
            {state.running ? 'Стоп' : 'Старт'}
          </Button>

          <div className="flex justify-between">
            {[
              ['correct', <ArrowLeft size={28} className="mr-2"/>],
              ['incorrect', <ArrowRight size={28} className="ml-2"/>]
            ].map(([type, icon]) => (
              <Button
                key={String(type)}
                onClick={() => updateScore(type as 'correct' | 'incorrect', 1)}
                className={`flex-1 py-4 md:py-5 text-xl md:text-2xl font-medium ${themeStyles.button} flex items-center justify-center ${type === 'correct' ? 'mr-3' : 'ml-3'} rounded-xl`}
                disabled={!state.running}
              >
                {type === 'correct' && icon}
                <span className="font-bold">+</span>
                {type === 'incorrect' && icon}
              </Button>
            ))}
          </div>

          <Button
            onClick={resetScore}
            className={`w-full py-4 md:py-5 text-xl md:text-2xl font-medium ${themeStyles.button} flex items-center justify-center mt-4 md:mt-6 rounded-xl`}
            disabled={!state.running}
          >
            <ArrowDown size={28} className="mr-3"/>
            <span className="font-bold">Сброс</span>
          </Button>
        </CardContent>
      </Card>

      {state.historyOpen ? <StatsModal/> : null}
    </div>
  );
};

export default ScoreCounter;
