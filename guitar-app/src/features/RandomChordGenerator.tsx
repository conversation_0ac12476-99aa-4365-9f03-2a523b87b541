"use client"

import { useState, useRef, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from "../shared/ui/button"
import { Card, CardContent } from "../shared/ui/card"
import { Slider } from "../shared/ui/slider"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../shared/ui/dialog"
import { Checkbox } from "../shared/ui/checkbox"
import { Switch } from "../shared/ui/switch"
import { Settings, Volume2, VolumeX } from "lucide-react"
import { initialChordTypes, degreeToSemitone } from "../constants/musicTheory"
import BackButton from "../shared/ui/back-button"
import GuitarFretboard, { NoteDisplay } from "../shared/components/GuitarFretboard"

// Массив нот для генерации аккордов (включает и диезы, и бемоли)
const notes = ['C', 'C#', 'Db', 'D', 'D#', 'Eb', 'E', 'F', 'F#', 'Gb', 'G', 'G#', 'Ab', 'A', 'A#', 'Bb', 'B']

// Массив для определения MIDI-нот (только диезы)
const midiNotes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']



const convertToUnicode = (chord: string) => {
  return chord
    .replace(/C#/g, 'C♯')
    .replace(/D#/g, 'D♯')
    .replace(/F#/g, 'F♯')
    .replace(/G#/g, 'G♯')
    .replace(/A#/g, 'A♯')
    .replace(/Db/g, 'D♭')
    .replace(/Eb/g, 'E♭')
    .replace(/Gb/g, 'G♭')
    .replace(/Ab/g, 'A♭')
    .replace(/Bb/g, 'B♭')
    .replace(/maj/g, 'Δ')
    .replace(/dim/g, '°')
    .replace(/ø/g, 'ø')
    .replace(/add/g, 'add')
    .replace(/sus/g, 'sus')
    .replace(/\((\d+)$/g, '$1')
    .replace(/b5/g, '♭5')
    .replace(/b9/g, '♭9')
    .replace(/#5/g, '♯5')
    .replace(/2/g, '²')
    .replace(/4/g, '⁴')
    .replace(/6/g, '⁶')
    .replace(/7/g, '⁷')
    .replace(/9/g, '⁹')
}

// Константы для строя гитары и названий нот
const NOTE_NAMES_SHARP = ['C', 'C♯', 'D', 'D♯', 'E', 'F', 'F♯', 'G', 'G♯', 'A', 'A♯', 'B'];
const NOTE_NAMES_FLAT = ['C', 'D♭', 'D', 'E♭', 'E', 'F', 'G♭', 'G', 'A♭', 'A', 'B♭', 'B'];
const OPEN_STRING_MIDI = [40, 45, 50, 55, 59, 64]; // E2, A2, D3, G3, B3, E4

// Функция для преобразования MIDI-ноты в позиции на грифе
const getMidiNotePositions = (midiNote: number, useFlats: boolean = false): { string: number, fret: number, label: string }[] => {
  // Выбираем массив названий нот в зависимости от того, используем ли мы бемоли
  const noteNames = useFlats ? NOTE_NAMES_FLAT : NOTE_NAMES_SHARP;
  const noteName = noteNames[midiNote % 12];
  const positions = [];

  // Для каждой струны находим позицию ноты
  for (let string = 1; string <= 6; string++) {
    const openNote = OPEN_STRING_MIDI[string - 1];
    let fret = (midiNote - openNote) % 12;
    if (fret < 0) fret += 12;

    // Инвертируем номер струны для правильного отображения в GuitarFretboard
    // (в GuitarFretboard 6-я струна внизу, 1-я струна вверху)
    const invertedString = 7 - string;

    // Добавляем основную позицию и позицию на октаву выше (если в пределах грифа)
    positions.push({ string: invertedString, fret, label: noteName });
    if (fret + 12 <= 12) positions.push({ string: invertedString, fret: fret + 12, label: noteName });
  }

  return positions;
};

export default function RandomChordGenerator() {
  const [currentChord, setCurrentChord] = useState('')
  const [nextChord, setNextChord] = useState('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [tempo, setTempo] = useState(120)
  const [currentBeat, setCurrentBeat] = useState(0)
  const [chordTypes, setChordTypes] = useState(initialChordTypes)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isSoundOn, setIsSoundOn] = useState(true)
  // Загружаем настройку отображения грифа из localStorage или используем true по умолчанию
  const [showFretboard, setShowFretboard] = useState(() => {
    const saved = localStorage.getItem('showFretboard')
    return saved !== null ? JSON.parse(saved) : true
  })
  const [fretboardNotes, setFretboardNotes] = useState<NoteDisplay[]>([])
  const audioContextRef = useRef<AudioContext | null>(null)
  const oscillatorsRef = useRef<OscillatorNode[]>([])
  const metronomeIntervalRef = useRef<number | null>(null)

  // Простая таблица соответствия бемолей и диезов для MIDI
  const bemolToSharp: {[key: string]: string} = {
    'Db': 'C#',
    'Eb': 'D#',
    'Gb': 'F#',
    'Ab': 'G#',
    'Bb': 'A#'
  };

  const generateRandomChord = useCallback(() => {
    const selectedChordTypes = chordTypes.flatMap(category =>
      category.types.filter(type => type.selected)
    )
    if (selectedChordTypes.length === 0) return ''

    // Используем все ноты, включая бемоли
    const randomNote = notes[Math.floor(Math.random() * notes.length)]
    const randomChordType = selectedChordTypes[Math.floor(Math.random() * selectedChordTypes.length)]
    return `${randomNote}${randomChordType.name}`
  }, [chordTypes, notes])

  const midiToFrequency = (midi: number) => {
    return 440 * Math.pow(2, (midi - 69) / 12)
  }

  const getChordMidiNotes = useCallback((chord: string) => {
    // Обработка как диезов, так и бемолей
    const root = chord[0] + (chord.length > 1 && (chord[1] === '#' || chord[1] === 'b') ? chord[1] : '')
    const chordType = chordTypes.flatMap(category => category.types).find(type => chord.slice(root.length) === type.name)

    // Преобразуем бемоль в эквивалентный диез для MIDI
    let midiRoot = root;
    if (root.length > 1 && root[1] === 'b') {
      midiRoot = bemolToSharp[root] || root;
    }

    // Находим индекс ноты в массиве midiNotes
    let rootMidi = midiNotes.indexOf(midiRoot[0]) + 60; // Start from middle C (MIDI note 60)
    if (midiRoot.length > 1 && midiRoot[1] === '#') {
      rootMidi += 1; // Добавляем 1 для диеза
    }

    if (!chordType) return [rootMidi]

    // Преобразуем ступени в полутоны
    return chordType.formula.map(degree => rootMidi + degreeToSemitone[degree])
  }, [chordTypes, midiNotes, bemolToSharp, degreeToSemitone])

  const createStringOscillator = useCallback((ctx: AudioContext, freq: number) => {
    const osc = ctx.createOscillator()
    const gain = ctx.createGain()
    const filter = ctx.createBiquadFilter()

    osc.type = 'sawtooth'
    osc.frequency.setValueAtTime(freq, ctx.currentTime)

    filter.type = 'lowpass'
    filter.frequency.setValueAtTime(freq * 2, ctx.currentTime)
    filter.Q.setValueAtTime(10, ctx.currentTime)

    gain.gain.setValueAtTime(0, ctx.currentTime)
    gain.gain.linearRampToValueAtTime(0.2, ctx.currentTime + 0.02)
    gain.gain.exponentialRampToValueAtTime(0.001, ctx.currentTime + 2)

    osc.connect(filter)
    filter.connect(gain)
    gain.connect(ctx.destination)

    osc.start()
    return osc
  }, [])

  const playChord = useCallback((chord: string) => {
    if (!isSoundOn) return;

    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }

    const ctx = audioContextRef.current
    const chordMidiNotes = getChordMidiNotes(chord)

    // Stop previous oscillators
    oscillatorsRef.current.forEach(osc => osc.stop())
    oscillatorsRef.current = []

    // Create new oscillators for each MIDI note in the chord
    chordMidiNotes.forEach(midiNote => {
      const freq = midiToFrequency(midiNote)
      const osc = createStringOscillator(ctx, freq)
      oscillatorsRef.current.push(osc)
    })

    // Обновляем ноты на грифе
    const newFretboardNotes: NoteDisplay[] = [];



    // Определяем цвета для разных нот аккорда
    const noteColors = [
      'bg-green-600', // Корень (зеленый)
      'bg-blue-600',  // Терция (синий)
      'bg-purple-600', // Квинта (фиолетовый)
      'bg-orange-600', // Септима (оранжевый)
      'bg-yellow-600', // Другие ноты (желтый)
    ];

    // Определяем, содержит ли аккорд бемоли
    const hasFlats = chord.includes('b');

    // Для каждой MIDI-ноты находим все позиции на грифе
    chordMidiNotes.forEach((midiNote, index) => {
      // Транспонируем ноты в диапазон гитары (E2-E5)
      // Если нота выше E5 (MIDI 76), опускаем ее на октаву
      // Если нота ниже E2 (MIDI 40), поднимаем ее на октаву
      let transposedMidiNote = midiNote;
      while (transposedMidiNote > 76) transposedMidiNote -= 12;
      while (transposedMidiNote < 40) transposedMidiNote += 12;

      // Передаем флаг использования бемолей в функцию getMidiNotePositions
      const positions = getMidiNotePositions(transposedMidiNote, hasFlats);

      // Определяем цвет для ноты (по ее позиции в аккорде)
      const colorIndex = Math.min(index, noteColors.length - 1);
      const noteColor = noteColors[colorIndex];

      // Проверяем, является ли эта нота корневой
      const isRoot = index === 0;

      // Добавляем каждую позицию в массив нот для отображения
      positions.forEach(pos => {
        newFretboardNotes.push({
          id: `${pos.string}-${pos.fret}`,
          string: pos.string,
          fret: pos.fret,
          label: pos.label,
          color: noteColor,
          isRoot: isRoot,
          // Атрибут data-is-root удален, так как мы используем селектор по цвету
        });
      });
    });


    setFretboardNotes(newFretboardNotes);
  }, [getChordMidiNotes, createStringOscillator, isSoundOn, getMidiNotePositions])

  const playMetronomeClick = useCallback((isDownbeat: boolean) => {
    if (!isSoundOn) return;

    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }

    const ctx = audioContextRef.current
    const osc = ctx.createOscillator()
    const gain = ctx.createGain()

    osc.type = 'sine'
    osc.frequency.setValueAtTime(isDownbeat ? 880 : 440, ctx.currentTime) // A5 for downbeat, A4 for other beats

    gain.gain.setValueAtTime(0, ctx.currentTime)
    gain.gain.linearRampToValueAtTime(isDownbeat ? 0.5 : 0.3, ctx.currentTime + 0.001)
    gain.gain.exponentialRampToValueAtTime(0.001, ctx.currentTime + 0.1)

    osc.connect(gain)
    gain.connect(ctx.destination)

    osc.start()
    osc.stop(ctx.currentTime + 0.1)
  }, [isSoundOn])

  const stopChord = useCallback(() => {
    oscillatorsRef.current.forEach(osc => osc.stop())
    oscillatorsRef.current = []
    setFretboardNotes([]) // Очищаем ноты на грифе
  }, [setFretboardNotes])

  // Функция для генерации нового аккорда, отличного от текущего
  const getNextChord = useCallback((currentChord: string) => {
    let newChord = generateRandomChord();
    // Пробуем еще раз, если новый аккорд совпадает с текущим
    if (newChord === currentChord && notes.length > 1) {
      newChord = generateRandomChord();
    }
    return newChord;
  }, [generateRandomChord, notes])

  const changeChord = useCallback(() => {
    setCurrentChord(nextChord)
    playChord(nextChord)
    setNextChord(getNextChord(nextChord))
  }, [nextChord, playChord, getNextChord])

  const toggleSound = useCallback(() => {
    setIsSoundOn(prev => !prev);
  }, []);

  const togglePlay = useCallback(() => {
    if (isPlaying) {
      stopChord()
      if (metronomeIntervalRef.current) {
        clearInterval(metronomeIntervalRef.current)
        metronomeIntervalRef.current = null
      }
      setIsPlaying(false)
      setCurrentBeat(0)
      setCurrentChord('')
      setNextChord('')
    } else {
      setIsPlaying(true)
      const initialChord = generateRandomChord()
      const initialNextChord = getNextChord(initialChord)

      setCurrentChord(initialChord)
      setNextChord(initialNextChord)
      playChord(initialChord)
      playMetronomeClick(true)
      setCurrentBeat(1)

      const beatInterval = (60 / tempo) * 1000 // Convert BPM to milliseconds per beat
      metronomeIntervalRef.current = window.setInterval(() => {
        setCurrentBeat((prevBeat) => {
          const nextBeat = (prevBeat % 4) + 1
          playMetronomeClick(nextBeat === 1)
          if (nextBeat === 1) {
            changeChord()
          }
          return nextBeat
        })
      }, beatInterval)
    }
  }, [isPlaying, tempo, generateRandomChord, getNextChord, playChord, playMetronomeClick, stopChord, changeChord])

  useEffect(() => {
    return () => {
      if (metronomeIntervalRef.current) {
        clearInterval(metronomeIntervalRef.current)
      }
      stopChord()
    }
  }, [stopChord])

  useEffect(() => {
    if (isPlaying) {
      if (metronomeIntervalRef.current) {
        clearInterval(metronomeIntervalRef.current)
      }
      const beatInterval = (60 / tempo) * 1000
      metronomeIntervalRef.current = window.setInterval(() => {
        setCurrentBeat((prevBeat) => {
          const nextBeat = (prevBeat % 4) + 1
          playMetronomeClick(nextBeat === 1)
          if (nextBeat === 1) {
            changeChord()
          }
          return nextBeat
        })
      }, beatInterval)
    }
    return () => {
      if (metronomeIntervalRef.current) {
        clearInterval(metronomeIntervalRef.current)
      }
    }
  }, [tempo, isPlaying, playMetronomeClick, changeChord])

  const handleChordTypeChange = (categoryIndex: number, typeIndex: number, checked: boolean) => {
    setChordTypes(prevTypes => {
      const newTypes = [...prevTypes]
      newTypes[categoryIndex].types[typeIndex].selected = checked
      return newTypes
    })
  }

  const handleSelectAll = (categoryIndex: number) => {
    setChordTypes(prevTypes => {
      const newTypes = [...prevTypes]
      newTypes[categoryIndex].types = newTypes[categoryIndex].types.map(type => ({...type, selected: true}))
      return newTypes
    })
  }

  const handleDeselectAll = (categoryIndex: number) => {
    setChordTypes(prevTypes => {
      const newTypes = [...prevTypes]
      newTypes[categoryIndex].types = newTypes[categoryIndex].types.map(type => ({...type, selected: false}))
      return newTypes
    })
  }



  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 relative">
      {/* Кнопка "Назад" - перемещена правее, чтобы не перекрывать гамбургер-меню */}
      <BackButton className="absolute top-4 left-16" />

      <Card className="w-full max-w-md mx-auto">
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <Button variant="ghost" size="sm" onClick={toggleSound}>
              {isSoundOn ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
            </Button>
            <h1 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100">Генератор аккордов</h1>
            <Button variant="ghost" size="sm" onClick={() => setIsSettingsOpen(true)}>
              <Settings className="h-5 w-5" />
            </Button>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md mb-6 p-4 rounded-lg h-[180px] flex flex-col items-center justify-center">
            {!currentChord ? (
              <h2 className="text-5xl font-bold text-center text-gray-900 dark:text-gray-100">
                {isPlaying ? '...' : 'Нажмите СТАРТ'}
              </h2>
            ) : (
              <>
                {/* Измененный блок для горизонтального отображения аккордов */}
                <div className="flex items-center justify-center space-x-4 mb-2"> {/* Используем items-baseline, уменьшен mb */}
                  <h2 className="text-5xl font-bold text-gray-900 dark:text-gray-100">{convertToUnicode(currentChord)}</h2>
                  <h3 className="text-2xl text-gray-600 dark:text-gray-400">{nextChord ? `→ ${convertToUnicode(nextChord)}` : ''}</h3> {/* Добавим стрелку */}
                </div>
                {currentBeat > 0 && (
                  <div className="flex items-center space-x-3 mt-4"> {/* Сохраняем индикаторы */}
                    {[1, 2, 3, 4].map((beat) => (
                      <div
                        key={beat}
                        className={`w-3 h-3 rounded-full ${currentBeat === beat ? 'bg-blue-600 dark:bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Отображение грифа гитары с нотами аккорда */}
          {showFretboard && (
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md mb-6 p-4 rounded-lg min-h-[250px]">
              <GuitarFretboard notes={fretboardNotes} className="mb-4" />

              <style>{`
                /* Стили для компактного отображения грифа */
                .flex.items-center.relative {
                  height: clamp(0.8rem, 1.2vw, 1.2rem) !important;
                }
                /* Стили для корневой ноты (квадрат со скругленными краями) */
                .bg-green-600.rounded-full {
                  border-radius: 0.25rem !important;
                }
              `}</style>

              {/* Легенда цветов - отображается только когда есть ноты */}
              {fretboardNotes.length > 0 && (
                <div className="flex flex-wrap justify-center gap-2 mt-2">
                  <div className="flex items-center"><div className="w-3 h-3 bg-green-600 mr-1 rounded-sm"></div><span className="text-xs">R</span></div>
                  <div className="flex items-center"><div className="w-3 h-3 rounded-full bg-blue-600 mr-1"></div><span className="text-xs">3</span></div>
                  <div className="flex items-center"><div className="w-3 h-3 rounded-full bg-purple-600 mr-1"></div><span className="text-xs">5</span></div>
                  <div className="flex items-center"><div className="w-3 h-3 rounded-full bg-orange-600 mr-1"></div><span className="text-xs">7</span></div>
                  <div className="flex items-center"><div className="w-3 h-3 rounded-full bg-yellow-600 mr-1"></div><span className="text-xs">Other</span></div>
                </div>
              )}
            </div>
          )}

          <div className="flex items-center space-x-4 mb-6">
            <span>Темп: {tempo} BPM</span>
            <Slider
              value={tempo}
              onChange={(e) => setTempo(parseInt(e.target.value))}
              min={60}
              max={240}
              step={1}
              className="flex-grow"
            />
          </div>

          <Button
            onClick={togglePlay}
            variant={isPlaying ? "outline" : "primary"}
            size="lg"
            className={`w-full ${isPlaying ? 'bg-red-600 hover:bg-red-700 text-white border-red-600' : 'bg-green-600 hover:bg-green-700 text-white'}`}
          >
            {isPlaying ? 'СТОП' : 'СТАРТ'}
          </Button>

          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Настройки аккордов</DialogTitle>
              </DialogHeader>

              {/* Настройки интерфейса */}
              <div className="border-b pb-4 mb-4">
                <h3 className="text-lg font-semibold mb-2">Настройки интерфейса</h3>
                <div className="flex items-center justify-between">
                  <label htmlFor="show-fretboard" className="text-sm font-medium">
                    Отображать гриф гитары
                  </label>
                  <Switch
                    id="show-fretboard"
                    checked={showFretboard}
                    onCheckedChange={(checked) => {
                      setShowFretboard(checked)
                      localStorage.setItem('showFretboard', JSON.stringify(checked))
                    }}
                  />
                </div>
              </div>

              {/* Настройки типов аккордов */}
              <div className="grid gap-4">
                {chordTypes.map((category, categoryIndex) => (
                  <div key={category.category} className="border-b pb-4">
                    <h3 className="text-lg font-semibold mb-2">{category.category}</h3>
                    <div className="flex space-x-2 mb-2">
                      <Button onClick={() => handleSelectAll(categoryIndex)} size="sm" variant="outline">Выбрать все</Button>
                      <Button onClick={() => handleDeselectAll(categoryIndex)} size="sm" variant="outline">Снять все</Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {category.types.map((type, typeIndex) => (
                        <div key={type.name} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${category.category}-${type.name}`}
                            checked={type.selected}
                            onCheckedChange={(checked: boolean) => handleChordTypeChange(categoryIndex, typeIndex, checked)}
                          />
                          <label htmlFor={`${category.category}-${type.name}`} className="text-sm font-medium">
                            {type.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  )
}