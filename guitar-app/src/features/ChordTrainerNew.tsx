import React, { useEffect, useCallback, useReducer } from 'react'
import { Volume2, VolumeX, <PERSON>tings, ChevronLeft, ChevronRight, Shuffle } from 'lucide-react'
import BackButton from "../shared/ui/back-button"

// Импорт компонентов shadcn/ui
import { Button } from "../components/ui/button"
import { Card, CardContent } from "../components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select"
import { Slider } from "../components/ui/slider"
import { Badge } from "../components/ui/badge"

// Импорт общих компонентов и хуков
import ChordDiagram from '../shared/components/ChordDiagram'
import ChordSettingsNew from '../shared/components/ChordSettingsNew'
import { useChords, useCurrentChord } from '../shared/hooks/useChords'
import { playChord } from '../shared/utils/audio'

// -------------------- Типы и интерфейсы --------------------

interface ChordState {
  isPlaying: boolean
  showInterval: number
  duration: number
  timeLeft: number
  chordTimeLeft: number
  isSettingsOpen: boolean
  isAudioEnabled: boolean
  isRandomPlay: boolean
  useAlternativeFingerings: boolean
}

// -------------------- Утилиты --------------------

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60).toString().padStart(1, '0')
  const remainingSeconds = (seconds % 60).toString().padStart(2, '0')
  return `${minutes}:${remainingSeconds}`
}

// -------------------- Редьюсер --------------------

type Action =
  | { type: 'TOGGLE_PLAY' }
  | { type: 'SET_SHOW_INTERVAL', payload: number }
  | { type: 'SET_DURATION', payload: number }
  | { type: 'SET_TIME_LEFT', payload: number }
  | { type: 'SET_CHORD_TIME_LEFT', payload: number }
  | { type: 'TOGGLE_SETTINGS' }
  | { type: 'TOGGLE_AUDIO' }
  | { type: 'TOGGLE_RANDOM_PLAY' }
  | { type: 'TOGGLE_ALTERNATIVE_FINGERINGS' }

function reducer(state: ChordState, action: Action): ChordState {
  switch (action.type) {
    case 'TOGGLE_PLAY':
      return {
        ...state,
        isPlaying: !state.isPlaying,
        timeLeft: !state.isPlaying ? state.duration * 60 : state.timeLeft,
        chordTimeLeft: !state.isPlaying ? state.showInterval : state.chordTimeLeft
      }
    case 'SET_SHOW_INTERVAL':
      return { ...state, showInterval: action.payload }
    case 'SET_DURATION':
      return { ...state, duration: action.payload }
    case 'SET_TIME_LEFT':
      return { ...state, timeLeft: action.payload }
    case 'SET_CHORD_TIME_LEFT':
      return { ...state, chordTimeLeft: action.payload }
    case 'TOGGLE_SETTINGS':
      return { ...state, isSettingsOpen: !state.isSettingsOpen }
    case 'TOGGLE_AUDIO':
      return { ...state, isAudioEnabled: !state.isAudioEnabled }
    case 'TOGGLE_RANDOM_PLAY':
      return { ...state, isRandomPlay: !state.isRandomPlay }
    case 'TOGGLE_ALTERNATIVE_FINGERINGS':
      return { ...state, useAlternativeFingerings: !state.useAlternativeFingerings }
    default:
      return state
  }
}

// -------------------- Основной компонент --------------------

export default function ChordTrainerNew() {
  // Используем общие хуки для работы с аккордами
  const {
    enabledCategories,
    enabledChords,
    availableChords,
    toggleCategory,
    toggleChord,
    selectAllCategories,
    selectAllChords
  } = useChords();

  const {
    currentChord,
    setChord,
    currentPattern,
    variation,
    nextVariation,
    prevVariation,
    totalVariations
  } = useCurrentChord();

  // Состояние
  const [state, dispatch] = useReducer(reducer, {
    isPlaying: false,
    showInterval: 10,
    duration: 5,
    timeLeft: 5 * 60,
    chordTimeLeft: 10,
    isSettingsOpen: false,
    isAudioEnabled: true,
    isRandomPlay: false,
    useAlternativeFingerings: false
  });

  // Колбэки
  const selectNextChord = useCallback(() => {
    if (availableChords.length === 0) return;

    if (state.isRandomPlay) {
      // Выбираем случайный аккорд
      const randomIndex = Math.floor(Math.random() * availableChords.length);
      setChord(availableChords[randomIndex]);
    } else if (state.useAlternativeFingerings && currentChord && variation < totalVariations - 1) {
      // Переходим к следующей вариации текущего аккорда
      nextVariation();
    } else {
      // Переходим к следующему аккорду в списке
      if (currentChord && availableChords.length > 0) {
        const currentIndex = availableChords.findIndex(chord => chord.name === currentChord.name);
        const nextIndex = (currentIndex + 1) % availableChords.length;
        setChord(availableChords[nextIndex]);
      } else if (availableChords.length > 0) {
        setChord(availableChords[0]);
      }
    }

    // Сбрасываем таймер аккорда
    dispatch({ type: 'SET_CHORD_TIME_LEFT', payload: state.showInterval });
  }, [
    availableChords,
    state.isRandomPlay,
    state.useAlternativeFingerings,
    state.showInterval,
    currentChord,
    variation,
    totalVariations,
    setChord,
    nextVariation
  ]);

  const togglePlay = useCallback(() => {
    if (!state.isPlaying && availableChords.length > 0) {
      // Если нет выбранного аккорда, выбираем первый доступный
      if (!currentChord && availableChords.length > 0) {
        setChord(availableChords[0]);
      }

      // Воспроизводим аккорд
      if (currentPattern && state.isAudioEnabled) {
        playChord(currentPattern, state.isAudioEnabled);
      }
    }

    dispatch({ type: 'TOGGLE_PLAY' });
  }, [
    state.isPlaying,
    state.isAudioEnabled,
    availableChords,
    currentChord,
    currentPattern,
    setChord
  ]);

  const changeChord = useCallback((direction: number, changeVariation: boolean) => {
    if (availableChords.length === 0) return;

    if (changeVariation && currentChord) {
      // Изменяем вариацию текущего аккорда
      if (direction > 0) {
        nextVariation();
      } else {
        prevVariation();
      }
    } else {
      // Переходим к следующему/предыдущему аккорду
      if (currentChord) {
        const currentIndex = availableChords.findIndex(chord => chord.name === currentChord.name);
        const newIndex = (currentIndex + direction + availableChords.length) % availableChords.length;
        setChord(availableChords[newIndex]);
      } else if (availableChords.length > 0) {
        setChord(availableChords[0]);
      }
    }

    // Удалено воспроизведение аккорда здесь, так как оно будет происходить в useEffect
  }, [
    availableChords,
    currentChord,
    setChord,
    nextVariation,
    prevVariation
  ]);

  // Эффекты
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (state.isPlaying) {
      intervalId = setInterval(() => {
        dispatch({ type: 'SET_TIME_LEFT', payload: Math.max(0, state.timeLeft - 1) });
        dispatch({ type: 'SET_CHORD_TIME_LEFT', payload: Math.max(0, state.chordTimeLeft - 1) });

        if (state.chordTimeLeft <= 1) {
          selectNextChord();
        }

        if (state.timeLeft <= 1) {
          dispatch({ type: 'TOGGLE_PLAY' });
        }
      }, 1000);
    }

    return () => {
      clearInterval(intervalId);
    };
  }, [state.isPlaying, state.timeLeft, state.chordTimeLeft, selectNextChord]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space') {
        event.preventDefault();
        togglePlay();
      } else if (!state.isPlaying && !state.isSettingsOpen) {
        switch (event.key) {
          case 'ArrowLeft':
            changeChord(-1, false);
            break;
          case 'ArrowRight':
            changeChord(1, false);
            break;
          case 'ArrowUp':
            changeChord(1, true);
            break;
          case 'ArrowDown':
            changeChord(-1, true);
            break;
          case 'Enter':
            dispatch({ type: 'TOGGLE_SETTINGS' });
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.isPlaying, state.isSettingsOpen, changeChord, togglePlay]);

  // Воспроизведение аккорда при изменении
  useEffect(() => {
    if (currentPattern && state.isAudioEnabled) {
      // Воспроизводим аккорд при изменении паттерна, если включен звук
      // Это сработает как при автоматическом переключении, так и при ручном
      playChord(currentPattern, state.isAudioEnabled);
    }
  }, [currentPattern, state.isAudioEnabled]);

  // Рендер

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 relative">
      {/* Кнопка "Назад" */}
      <BackButton className="absolute top-4 left-16" />

      <Card className="w-full max-w-md mx-auto bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg">
        <CardContent className="p-6">
          {/* Заголовок */}
          <div className="flex justify-between items-center mb-6 md:mb-8">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => dispatch({ type: 'TOGGLE_AUDIO' })}
              aria-label={state.isAudioEnabled ? "Выключить звук" : "Включить звук"}
              className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              {state.isAudioEnabled ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
            </Button>
            <h2 className="text-xl md:text-2xl font-bold text-center text-gray-900 dark:text-gray-100">Тренажер аккордов</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => dispatch({ type: 'TOGGLE_SETTINGS' })}
              aria-label="Открыть настройки"
              className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Settings className="h-5 w-5" />
            </Button>

            {/* Диалог настроек */}
            <ChordSettingsNew
              open={state.isSettingsOpen}
              onOpenChange={(open) => dispatch({ type: 'TOGGLE_SETTINGS' })}
              enabledCategories={enabledCategories}
              enabledChords={enabledChords}
              onToggleCategory={toggleCategory}
              onToggleChord={toggleChord}
              onSelectAllCategories={selectAllCategories}
              onSelectAllChords={selectAllChords}
              useAlternativeFingerings={state.useAlternativeFingerings}
              onToggleAlternativeFingerings={() => dispatch({ type: 'TOGGLE_ALTERNATIVE_FINGERINGS' })}
            />
          </div>

          {/* Выбор аккорда */}
          <div className="flex items-center justify-center mb-4 md:mb-6">
            <Button
              variant="outline"
              size="icon"
              onClick={() => !state.isPlaying && availableChords.length > 0 && changeChord(-1, false)}
              disabled={state.isPlaying || availableChords.length === 0}
              aria-label="Предыдущий аккорд"
              className="h-12 w-12 md:h-14 md:w-14 rounded-r-none"
            >
              <ChevronLeft className="h-6 w-6 md:h-7 md:w-7" />
            </Button>
            <Select
              value={currentChord?.name || ''}
              onValueChange={(value: string) => {
                const selectedChord = availableChords.find(chord => chord.name === value);
                if (selectedChord) setChord(selectedChord);
              }}
              disabled={state.isPlaying || availableChords.length === 0}
            >
              <SelectTrigger className="flex-1 h-12 md:h-14 rounded-none border-l-0 border-r-0 text-xl sm:text-2xl md:text-3xl font-bold focus:ring-0 focus:ring-offset-0 px-2">
                <div className="w-full text-center">
                  <SelectValue placeholder="Выберите аккорд" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {availableChords.map(chord => (
                  <SelectItem key={chord.name} value={chord.name}>
                    {chord.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="icon"
              onClick={() => !state.isPlaying && availableChords.length > 0 && changeChord(1, false)}
              disabled={state.isPlaying || availableChords.length === 0}
              aria-label="Следующий аккорд"
              className="h-12 w-12 md:h-14 md:w-14 rounded-l-none"
            >
              <ChevronRight className="h-6 w-6 md:h-7 md:w-7" />
            </Button>
          </div>

          {/* Диаграмма аккорда */}
          <div
            className="border border-gray-200 dark:border-gray-700 rounded-lg mb-6 md:mb-8 p-4 min-h-[220px] sm:min-h-[250px] cursor-pointer flex items-center justify-center bg-white dark:bg-gray-800 hover:shadow-md transition-all duration-300 ease-in-out"
            onClick={() => !state.isPlaying && changeChord(1, true)}
            role="button"
            tabIndex={state.isPlaying ? -1 : 0}
            aria-label="Изменить вариацию аккорда"
            onKeyDown={(e: React.KeyboardEvent) => !state.isPlaying && e.key === 'Enter' && changeChord(1, true)}
          >

            <ChordDiagram
              pattern={currentPattern || ''}
              variation={variation}
              totalVariations={totalVariations}
              onPlay={(pattern) => playChord(pattern, state.isAudioEnabled)}
              size="md"
            />
          </div>

          {/* Настройки */}
          <div className="flex flex-col space-y-3 mb-6 md:mb-8">
            {/* Таймер */}
            <div className="flex items-center w-full">
              <span className="text-sm text-gray-500 dark:text-gray-400 mr-2 w-16">Таймер:</span>
              <Slider
                min={1}
                max={60}
                step={1}
                value={[state.isPlaying ? state.timeLeft / 60 : state.duration]}
                onValueChange={(value: number[]) => !state.isPlaying && dispatch({ type: 'SET_DURATION', payload: value[0] })}
                disabled={state.isPlaying}
                className="flex-1"
              />
              <Badge variant="outline" className="ml-2 w-[3.5rem] justify-center font-mono">
                {state.isPlaying ? formatTime(state.timeLeft) : `${state.duration}:00`}
              </Badge>
            </div>

            {/* Интервал */}
            <div className="flex items-center w-full">
              <span className="text-sm text-gray-500 dark:text-gray-400 mr-2 w-16">Интервал:</span>
              <Slider
                value={[state.showInterval]}
                onValueChange={(value: number[]) => dispatch({ type: 'SET_SHOW_INTERVAL', payload: value[0] })}
                min={1}
                max={60}
                step={1}
                disabled={state.isPlaying}
                className="flex-1"
              />
              <Badge variant="outline" className="ml-2 w-[3.5rem] justify-center font-mono">
                {`0:${state.showInterval.toString().padStart(2, '0')}`}
              </Badge>
            </div>
          </div>

          {/* Управление */}
          <div className="flex flex-col items-center">
            <div className="flex w-full gap-2">
              <Button
                onClick={togglePlay}
                disabled={availableChords.length === 0}
                variant={state.isPlaying ? 'destructive' : 'success'}
                className="h-12 md:h-14 text-base md:text-lg flex-1"
                aria-label={state.isPlaying ? "Остановить игру" : "Начать игру"}
              >
                {state.isPlaying ? 'СТОП' : 'СТАРТ'}
              </Button>
              <Button
                onClick={() => dispatch({ type: 'TOGGLE_RANDOM_PLAY' })}
                variant={state.isRandomPlay ? 'default' : 'outline'}
                size="icon"
                className="h-12 md:h-14 w-12 md:w-14"
                aria-label={state.isRandomPlay ? "Отключить случайное воспроизведение" : "Включить случайное воспроизведение"}
                title={state.isRandomPlay ? "Отключить случайное воспроизведение" : "Включить случайное воспроизведение"}
              >
                <Shuffle className="h-6 w-6" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
