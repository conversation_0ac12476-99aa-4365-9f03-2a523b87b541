import React, { useState } from 'react';
import {
  CHROMATIC_SCALE,
  INTERVALS,
  CHORD_FORMULAS,
  SCALE_FORMULAS,
  transposeNote,
  buildChord,
  buildScale,
  formatChordName,
  formatIntervalName,
  formatFormula,
  Note
} from '../constants/MusicTheoryCore';

const MusicTheory: React.FC = () => {
  // Состояния для выбранных элементов
  const [selectedNote, setSelectedNote] = useState<Note>({ name: 'C' });
  const [selectedChordType, setSelectedChordType] = useState('major');
  const [selectedScaleType, setSelectedScaleType] = useState('major');
  const [selectedInterval, setSelectedInterval] = useState('P1');
  const [activeTab, setActiveTab] = useState('chords');

  // Вычисляем аккорд, гамму и интервал на основе выбранных элементов
  const chord = buildChord(selectedNote, CHORD_FORMULAS[selectedChordType]);
  const scale = buildScale(selectedNote, SCALE_FORMULAS[selectedScaleType]);
  const interval = INTERVALS[selectedInterval];
  const intervalNote = transposeNote(selectedNote, interval.semitones);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Music Theory Explorer</h1>
        <p className="text-center mb-8 text-gray-600 dark:text-gray-400">
          Интерактивный инструмент для изучения музыкальной теории
        </p>

        {/* Простые вкладки без использования компонента Tabs */}
        <div className="mb-8">
          <div className="flex border-b border-gray-200 dark:border-gray-700">
            <button
              className={`px-4 py-2 ${activeTab === 'chords' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500 dark:text-gray-400'}`}
              onClick={() => setActiveTab('chords')}
            >
              Аккорды
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'scales' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500 dark:text-gray-400'}`}
              onClick={() => setActiveTab('scales')}
            >
              Гаммы
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'intervals' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500 dark:text-gray-400'}`}
              onClick={() => setActiveTab('intervals')}
            >
              Интервалы
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'reference' ? 'border-b-2 border-blue-500 font-medium' : 'text-gray-500 dark:text-gray-400'}`}
              onClick={() => setActiveTab('reference')}
            >
              Справочник
            </button>
          </div>
        </div>

        {/* Вкладка Аккорды */}
        {activeTab === 'chords' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Выберите ноту и тип аккорда</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Создайте аккорд, выбрав корневую ноту и тип аккорда
              </p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Корневая нота</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedNote.name}
                    onChange={(e) => setSelectedNote({ name: e.target.value as any })}
                  >
                    {CHROMATIC_SCALE.map((note) => (
                      <option key={note} value={note}>{note}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Тип аккорда</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedChordType}
                    onChange={(e) => setSelectedChordType(e.target.value)}
                  >
                    {Object.entries(CHORD_FORMULAS).map(([key, formula]) => (
                      <option key={key} value={key}>{formula.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">{formatChordName(selectedNote, CHORD_FORMULAS[selectedChordType])}</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Формула: {formatFormula(CHORD_FORMULAS[selectedChordType].steps)}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Ноты аккорда</h3>
                  <div className="flex flex-wrap gap-2">
                    {chord.map((note, index) => (
                      <div
                        key={index}
                        className={`px-4 py-2 rounded-md ${index === 0 ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                      >
                        {note.name}
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Структура</h3>
                  <div className="space-y-2">
                    {CHORD_FORMULAS[selectedChordType].steps.map((step, index) => (
                      <div key={index} className="flex justify-between">
                        <span>{step}</span>
                        <span>{chord[index]?.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Вкладка Гаммы */}
        {activeTab === 'scales' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Выберите ноту и тип гаммы</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Создайте гамму, выбрав корневую ноту и тип гаммы
              </p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Корневая нота</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedNote.name}
                    onChange={(e) => setSelectedNote({ name: e.target.value as any })}
                  >
                    {CHROMATIC_SCALE.map((note) => (
                      <option key={note} value={note}>{note}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Тип гаммы</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedScaleType}
                    onChange={(e) => setSelectedScaleType(e.target.value)}
                  >
                    {Object.entries(SCALE_FORMULAS).map(([key, formula]) => (
                      <option key={key} value={key}>{formula.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">{selectedNote.name} {SCALE_FORMULAS[selectedScaleType].name}</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Формула: {formatFormula(SCALE_FORMULAS[selectedScaleType].steps)}
              </p>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Ноты гаммы</h3>
                  <div className="flex flex-wrap gap-2">
                    {scale.map((note, index) => (
                      <div
                        key={index}
                        className={`px-4 py-2 rounded-md ${index === 0 ? 'bg-green-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                      >
                        {note.name}
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Структура</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {SCALE_FORMULAS[selectedScaleType].steps.map((step, index) => (
                      <div key={index} className="flex justify-between bg-gray-100 dark:bg-gray-800 p-2 rounded">
                        <span>{step}</span>
                        <span>{scale[index]?.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Вкладка Интервалы */}
        {activeTab === 'intervals' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Выберите ноту и интервал</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Изучите интервалы, выбрав начальную ноту и интервал
              </p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Начальная нота</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedNote.name}
                    onChange={(e) => setSelectedNote({ name: e.target.value as any })}
                  >
                    {CHROMATIC_SCALE.map((note) => (
                      <option key={note} value={note}>{note}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Интервал</label>
                  <select
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
                    value={selectedInterval}
                    onChange={(e) => setSelectedInterval(e.target.value)}
                  >
                    {Object.entries(INTERVALS).map(([key, interval]) => (
                      <option key={key} value={key}>{interval.name} ({key})</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">{formatIntervalName(interval)}</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {selectedNote.name} → {intervalNote.name} ({interval.semitones} полутонов)
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Визуализация</h3>
                  <div className="flex items-center justify-center h-32 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <div className="px-4 py-2 rounded-md bg-purple-500 text-white mr-8">
                      {selectedNote.name}
                    </div>
                    <div className="text-2xl">→</div>
                    <div className="px-4 py-2 rounded-md bg-pink-500 text-white ml-8">
                      {intervalNote.name}
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Характеристики</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Название:</span>
                      <span>{interval.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Сокращение:</span>
                      <span>{interval.shortName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Полутоны:</span>
                      <span>{interval.semitones}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Качество:</span>
                      <span>{interval.quality}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Ступень:</span>
                      <span>{interval.number}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Вкладка Справочник */}
        {activeTab === 'reference' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">Хроматический звукоряд</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                12 нот хроматического звукоряда
              </p>
              <div className="flex flex-wrap gap-2">
                {CHROMATIC_SCALE.map((note) => (
                  <div
                    key={note}
                    className="px-4 py-2 rounded-md bg-gray-200 dark:bg-gray-700"
                  >
                    {note}
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">Интервалы</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Основные музыкальные интервалы
              </p>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(INTERVALS).map(([key, interval]) => (
                  <div
                    key={key}
                    className="flex justify-between items-center p-2 rounded bg-gray-100 dark:bg-gray-800"
                  >
                    <span>{interval.name}</span>
                    <span className="font-bold">{key}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">Аккорды</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Формулы основных аккордов
              </p>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(CHORD_FORMULAS).map(([key, formula]) => (
                  <div
                    key={key}
                    className="p-2 rounded bg-gray-100 dark:bg-gray-800"
                  >
                    <div className="flex justify-between">
                      <span>{formula.name}</span>
                      <span className="font-bold">{key}</span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {formatFormula(formula.steps)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-2">Гаммы и лады</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Формулы основных гамм и ладов
              </p>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(SCALE_FORMULAS).map(([key, formula]) => (
                  <div
                    key={key}
                    className="p-2 rounded bg-gray-100 dark:bg-gray-800"
                  >
                    <div className="flex justify-between">
                      <span>{formula.name}</span>
                      <span className="font-bold">{key}</span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {formatFormula(formula.steps)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MusicTheory;
