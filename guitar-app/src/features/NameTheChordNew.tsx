'use client'

import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react'
import { Volume2, VolumeX, Settings, Plus, Minus, Timer, Gauge } from 'lucide-react'
import BackButton from "../shared/ui/back-button"

// Импорт компонентов shadcn/ui
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"

// Импорт общих компонентов и хуков
import ChordDiagram from '../shared/components/ChordDiagram'
import ChordSettingsNew from '../shared/components/ChordSettingsNew'
import { useChords, useCurrentChord } from '../shared/hooks/useChords'
import { playChord, playSound } from '../shared/utils/audio'

// -------------------- Типы и интерфейсы --------------------

interface GameState {
  options: string[]
  score: number
  mistakes: number
  isPlaying: boolean
  elapsedTime: number
  remainingTime: number
  buttonStates: Record<string, 'correct' | 'incorrect' | undefined>
  startTime: Date | null
  totalAttempts: number
  lastSpeed: number
  isSoundOn: boolean
  isTimedMode: boolean
  isSettingsOpen: boolean
  useAlternativeFingerings: boolean
}

// -------------------- Константы --------------------

const INITIAL_TIME = 180 // 3 минуты

// -------------------- Основной компонент --------------------

export default function NameTheChord() {
  // Используем общие хуки для работы с аккордами
  const {
    enabledCategories,
    enabledChords,
    availableChords,
    toggleCategory,
    toggleChord,
    selectAllCategories,
    selectAllChords,
    getRandomChord,
    getRandomOptions
  } = useChords();

  const {
    currentChord,
    currentPattern,
    setChord,
    totalVariations,
    setVariation
  } = useCurrentChord();

  // Состояние игры
  const [state, setState] = useState<GameState>({
    options: ['C', 'D', 'E', 'Am'], // Начальные опции для демонстрации
    score: 0,
    mistakes: 0,
    isPlaying: false,
    elapsedTime: 0,
    remainingTime: INITIAL_TIME,
    buttonStates: {},
    startTime: null,
    totalAttempts: 0,
    lastSpeed: 0,
    isSoundOn: true,
    isTimedMode: false,
    isSettingsOpen: false,
    useAlternativeFingerings: false
  });

  const [correctButton, setCorrectButton] = useState<string | null>(null);
  const audioContext = useRef<AudioContext | null>(null);
  // Используем useRef для хранения текущего аккорда без пересоздания функции
  const currentChordRef = useRef<any>(null);

  // Обновляем ссылку на текущий аккорд при его изменении
  useEffect(() => {
    currentChordRef.current = currentChord;
  }, [currentChord]);

  // Выбор случайного аккорда и вариантов ответа
  const selectRandomChord = useCallback(() => {
    // Получаем новый аккорд, исключая текущий
    let newChord;

    // Если есть текущий аккорд и доступно более одного аккорда
    if (currentChordRef.current && availableChords.length > 1) {
      // Фильтруем доступные аккорды, исключая текущий
      const filteredChords = availableChords.filter(chord => chord.name !== currentChordRef.current.name);
      // Выбираем случайный аккорд из отфильтрованного списка
      newChord = filteredChords[Math.floor(Math.random() * filteredChords.length)];
    } else {
      // Если нет текущего аккорда или доступен только один аккорд, используем стандартную функцию
      newChord = getRandomChord();
    }

    if (!newChord) return;

    setChord(newChord);

    // Если включено использование альтернативных аппликатур и у аккорда есть несколько вариаций
    if (state.useAlternativeFingerings && newChord.variations.length > 1) {
      // Выбираем случайную вариацию аккорда
      const randomVariation = Math.floor(Math.random() * newChord.variations.length);
      // Устанавливаем выбранную вариацию
      setVariation(randomVariation);
    } else {
      // Используем первую вариацию аккорда
      setVariation(0);
    }

    const newOptions = getRandomOptions(newChord, 4);

    setState(prevState => ({
      ...prevState,
      options: newOptions,
      buttonStates: {} // Сбрасываем состояние всех кнопок
    }));
  }, [getRandomChord, getRandomOptions, setChord, setVariation, state.useAlternativeFingerings, availableChords]);

  // Воспроизведение звукового сигнала
  const playTone = useCallback((frequency: number, duration: number) => {
    if (!state.isSoundOn) return;

    if (!audioContext.current) {
      audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }

    const oscillator = audioContext.current.createOscillator();
    const gainNode = audioContext.current.createGain();

    oscillator.frequency.setValueAtTime(frequency, audioContext.current.currentTime);
    gainNode.gain.setValueAtTime(0, audioContext.current.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1, audioContext.current.currentTime + 0.01);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.current.currentTime + duration);

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.current.destination);

    oscillator.start();
    oscillator.stop(audioContext.current.currentTime + duration);
  }, [state.isSoundOn]);

  // Обработка выбора аккорда пользователем - оптимизированный
  const handleGuess = useCallback((guess: string) => {
    if (!state.isPlaying || !currentChord) return;

    const isCorrect = guess === currentChord.name;

    // Воспроизведение звука вынесено за пределы setState для оптимизации
    if (isCorrect) {
      playTone(440, 0.1); // Высокий звук для правильного ответа
      setCorrectButton(guess);
      setTimeout(() => setCorrectButton(null), 500);
    } else {
      playTone(220, 0.1); // Низкий звук для неправильного ответа
    }

    setState(prevState => {
      if (isCorrect) {
        return {
          ...prevState,
          score: prevState.score + 1,
          buttonStates: { [guess]: 'correct' },
          totalAttempts: prevState.totalAttempts + 1
        };
      } else {
        return {
          ...prevState,
          mistakes: prevState.mistakes + 1,
          buttonStates: { ...prevState.buttonStates, [guess]: 'incorrect' },
          totalAttempts: prevState.totalAttempts + 1
        };
      }
    });

    if (isCorrect) {
      setTimeout(() => {
        selectRandomChord();
      }, 1000);
    }
  }, [state.isPlaying, currentChord, playTone, selectRandomChord]);

  // Запуск/остановка игры
  const togglePlay = () => {
    setState(prevState => {
      const newIsPlaying = !prevState.isPlaying;
      if (newIsPlaying) {
        // При нажатии Старт сбрасываем статистику
        return {
          ...prevState,
          isPlaying: true,
          score: 0,
          mistakes: 0,
          buttonStates: {},
          startTime: new Date(),
          elapsedTime: 0,
          totalAttempts: 0, // Сбрасываем счетчик попыток
          remainingTime: prevState.isTimedMode ? INITIAL_TIME : 0
        };
      } else {
        // При нажатии Стоп сохраняем статистику
        return {
          ...prevState,
          isPlaying: false,
          buttonStates: {},
          options: ['C', 'D', 'E', 'Am'], // Сбрасываем варианты ответов на начальные
          lastSpeed: Math.round(prevState.score / ((new Date().getTime() - (prevState.startTime?.getTime() || 0)) / 60000 || 1))
        };
      }
    });

    if (!state.isPlaying) {
      // При нажатии Старт выбираем случайный аккорд
      selectRandomChord();
      setTimeout(() => {
        if (currentChord && currentPattern && state.isSoundOn) {
          playChord(currentPattern, state.isSoundOn);
        }
      }, 100);
    } else {
      // При нажатии Стоп очищаем текущий аккорд
      setChord(null);
    }
  };

  // Включение/выключение звука
  const toggleSound = () => setState(prevState => ({
    ...prevState,
    isSoundOn: !prevState.isSoundOn
  }));

  // Включение/выключение использования альтернативных аппликатур
  const toggleAlternativeFingerings = () => setState(prevState => ({
    ...prevState,
    useAlternativeFingerings: !prevState.useAlternativeFingerings
  }));

  // Регулировка времени
  const adjustTime = (adjustment: number) => {
    if (state.isTimedMode) {
      setState(prevState => ({
        ...prevState,
        remainingTime: Math.max(60, Math.min(3600, prevState.remainingTime + adjustment))
      }));
    }
  };

  // Форматирование времени с гарантированной шириной
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60).toString().padStart(1, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${secs}`;
  };

  // Расчет текущей скорости (только правильные ответы) - мемоизация
  const currentSpeed = useMemo(() => {
    return state.isPlaying && state.startTime
      ? Math.round(state.score / ((new Date().getTime() - state.startTime.getTime()) / 60000 || 1))
      : state.lastSpeed;
  }, [state.isPlaying, state.startTime, state.score, state.lastSpeed]);

  // Расчет точности - мемоизация
  const accuracy = useMemo(() => {
    return state.score + state.mistakes > 0
      ? Math.round((state.score / (state.score + state.mistakes)) * 100)
      : 0;
  }, [state.score, state.mistakes]);

  // Эффекты

  // Таймер для отсчета времени - оптимизированный
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (state.isPlaying) {
      timer = setInterval(() => {
        setState(prevState => ({
          ...prevState,
          elapsedTime: prevState.elapsedTime + 1,
          remainingTime: prevState.isTimedMode ? prevState.remainingTime - 1 : prevState.remainingTime
        }));
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [state.isPlaying, state.isTimedMode]);

  // Выбор случайного аккорда при начале игры
  useEffect(() => {
    if (state.isPlaying) {
      selectRandomChord();
    } else {
      // Когда игра неактивна, сбрасываем аккорд
      setChord(null);
    }
  }, [state.isPlaying, selectRandomChord, setChord]);

  // Воспроизведение аккорда - оптимизированный
  useEffect(() => {
    if (state.isPlaying && currentChord && currentPattern && state.isSoundOn) {
      const timer = setTimeout(() => {
        playChord(currentPattern, state.isSoundOn);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [currentChord, currentPattern, state.isPlaying, state.isSoundOn, playChord]);

  // Очистка аудиоконтекста при размонтировании
  useEffect(() => {
    return () => {
      if (audioContext.current) {
        audioContext.current.close();
      }
    };
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 relative">
      {/* Кнопка "Назад" */}
      <BackButton className="absolute top-4 left-16" />

      <Card className="w-full max-w-md mx-auto bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
        <CardContent className="p-6">
          {/* Заголовок */}
          <div className="flex justify-between items-center mb-6 md:mb-8">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSound}
              aria-label={state.isSoundOn ? "Выключить звук" : "Включить звук"}
              className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              {state.isSoundOn ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
            </Button>
            <h2 className="text-xl md:text-2xl font-bold text-center text-gray-900 dark:text-gray-100">Назови аккорд</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setState(prev => ({ ...prev, isSettingsOpen: true }))}
              disabled={state.isPlaying}
              aria-label="Открыть настройки"
              className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Settings className="h-5 w-5" />
            </Button>

            {/* Диалог настроек */}
            <ChordSettingsNew
              open={state.isSettingsOpen}
              onOpenChange={(open) => setState(prev => ({ ...prev, isSettingsOpen: open }))}
              enabledCategories={enabledCategories}
              enabledChords={enabledChords}
              onToggleCategory={toggleCategory}
              onToggleChord={toggleChord}
              onSelectAllCategories={selectAllCategories}
              onSelectAllChords={selectAllChords}
              useAlternativeFingerings={state.useAlternativeFingerings}
              onToggleAlternativeFingerings={toggleAlternativeFingerings}
            />
          </div>

          {/* Статистика */}
          <div className="flex justify-between items-center p-3 rounded-lg mb-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
            {/* Счет */}
            <div className="flex items-center">
              <span className="text-gray-500 dark:text-gray-400 mr-1 font-medium text-sm">Счет:</span>
              <Badge variant="success" className="font-bold w-[2rem] text-center">{state.score}</Badge>
              <span className="mx-0.5 font-bold text-gray-500 dark:text-gray-400">:</span>
              <Badge variant="destructive" className="font-bold w-[2rem] text-center">{state.mistakes}</Badge>
            </div>

            {/* Точность */}
            <div className="flex items-center ml-1">
              <Badge variant="secondary" className="font-bold w-[3rem] text-center">{accuracy}%</Badge>
            </div>

            {/* Скорость */}
            <div className="flex items-center ml-1">
              <Gauge className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-0.5" />
              <Badge variant="outline" className="font-bold w-[2rem] text-center">{currentSpeed}</Badge>
              <span className="ml-0.5 text-gray-500 dark:text-gray-400 text-xs">акк/мин</span>
            </div>

            {/* Время */}
            <div className="flex items-center ml-1">
              <Timer className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-0.5" />
              <Badge variant="outline" className="font-bold w-[3.5rem] text-center font-mono">
                {state.isTimedMode ? formatTime(state.remainingTime) : formatTime(state.elapsedTime)}
              </Badge>
              {state.isTimedMode && !state.isPlaying && (
                <div className="flex ml-0.5">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => adjustTime(-60)}
                    className="h-4 w-4 p-0 mr-0.5"
                  >
                    <Minus className="h-2 w-2" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => adjustTime(60)}
                    className="h-4 w-4 p-0"
                  >
                    <Plus className="h-2 w-2" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Диаграмма аккорда */}
          <div
            className="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md min-h-[220px] sm:min-h-[250px] flex items-center justify-center p-4 transition-all duration-300 ease-in-out transform hover:scale-105 cursor-pointer"
            onClick={() => currentPattern && state.isSoundOn && playChord(currentPattern, state.isSoundOn)}
            role="button"
            tabIndex={0}
            aria-label="Прослушать аккорд"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && currentPattern && state.isSoundOn) {
                playChord(currentPattern, state.isSoundOn);
              }
            }}
          >
            {state.isPlaying && currentChord ? (
              <ChordDiagram
                pattern={currentPattern || ''}
                variation={currentChord?.variations.findIndex(v => v.pattern === currentPattern) || 0}
                totalVariations={totalVariations}
                onPlay={(pattern) => state.isSoundOn && playChord(pattern, state.isSoundOn)}
                size="md"
              />
            ) : (
              <ChordDiagram
                pattern="000000" // Пустой паттерн для отображения пустой диаграммы
                size="md"
              />
            )}
          </div>

          {/* Варианты ответов */}
          <div className="mb-6">
            <div className="grid grid-cols-2 gap-3 min-h-[120px]">
              {useMemo(() => {
                return state.isPlaying ? (
                  state.options.map((option, index) => (
                    <Button
                      key={index}
                      onClick={() => handleGuess(option)}
                      className="h-12 md:h-14 text-xl md:text-2xl lg:text-3xl font-bold"
                      variant={
                        state.buttonStates[option] === 'correct' || correctButton === option
                          ? 'success'
                          : state.buttonStates[option] === 'incorrect'
                          ? 'destructive'
                          : 'default'
                      }
                    >
                      {option}
                    </Button>
                  ))
                ) : (
                  // Пустые кнопки без названий аккордов
                  Array.from({ length: 4 }).map((_, index) => (
                    <Button
                      key={index}
                      className="h-12 md:h-14 text-xl md:text-2xl lg:text-3xl font-bold opacity-50"
                      disabled={true}
                    >
                      &nbsp;
                    </Button>
                  ))
                );
              }, [state.isPlaying, state.options, state.buttonStates, correctButton, handleGuess])}
            </div>
          </div>

          {/* Управление */}
          <div className="flex flex-col items-center">
            <Button
              onClick={togglePlay}
              variant={state.isPlaying ? 'destructive' : 'success'}
              className="w-full h-12 md:h-14 text-base md:text-lg font-medium"
              aria-label={state.isPlaying ? "Остановить игру" : "Начать игру"}
            >
              {state.isPlaying ? 'СТОП' : 'СТАРТ'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
