import React, { useState } from 'react';
// ThemeToggle удален - переключение темы только на главной странице
import GuitarNeckDiagram from '../shared/components/GuitarNeckDiagram';
import BackButton from '../shared/ui/back-button';

const GuitarNeckEditor: React.FC = () => {
  const [diagramState, setDiagramState] = useState<any>(null);

  const handleStateChange = (newState: any) => {
    setDiagramState(newState);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 relative">
      {/* Кнопка "Назад" - перемещена правее, чтобы не перекрывать гамбургер-меню */}
      <BackButton className="absolute top-4 left-16" />

      {/* Переключатель темы удален - доступен только на главной странице */}

      <div className="w-full max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-6 text-gray-900 dark:text-white">Редактор грифа гитары</h1>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-6 rounded-lg shadow-sm mb-8">
          <p className="text-base text-gray-600 dark:text-gray-400 mb-4 text-center">
            Создавайте и редактируйте диаграммы аккордов, гамм и других обозначений на грифе гитары.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-6 rounded-lg shadow-sm">
          <GuitarNeckDiagram
            initialState={diagramState}
            onChange={handleStateChange}
          />
        </div>
      </div>
    </div>
  );
};

export default GuitarNeckEditor;
