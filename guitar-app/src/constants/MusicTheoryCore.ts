// MusicTheoryCore.ts
// Универсальный модуль для работы с музыкальной теорией и гитарой.
// Содержит базовые типы, константы и утилиты для работы с нотами,
// интервалами, аккордами, гаммами и гитарным грифом.

// ======== БАЗОВЫЕ ТИПЫ ========

// Названия нот в хроматическом звукоряде
export type NoteName = 'C' | 'C#' | 'D' | 'D#' | 'E' | 'F' | 'F#' | 'G' | 'G#' | 'A' | 'A#' | 'B';

// Альтерации (знаки альтерации)
export type Accidental = 'natural' | 'sharp' | 'flat' | 'doubleSharp' | 'doubleFlat';

// Октавы (стандартный диапазон)
export type Octave = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

// Нота с названием, октавой и альтерацией
export interface Note {
  name: NoteName;
  octave?: Octave;
  accidental?: Accidental;
}

// Интервал между нотами
export interface Interval {
  name: string;           // Название (например, "Малая терция")
  shortName: string;      // Сокращенное название (например, "m3")
  semitones: number;      // Количество полутонов
  quality: 'perfect' | 'major' | 'minor' | 'augmented' | 'diminished';  // Качество интервала
  number: number;         // Ступень (1 для примы, 2 для секунды и т.д.)
}

// Степень с альтерацией в формуле (например, "1", "b3", "#4", "bb7")
export type FormulaStep = string;

// Формула аккорда в традиционной нотации (например, ["1", "3", "5"] для мажорного трезвучия)
export interface ChordFormula {
  name: string;           // Название (например, "Мажорный")
  steps: FormulaStep[];   // Ступени с альтерациями в виде строк
  symbol: string;         // Символ (например, "", "m", "7", "maj7")
}

// Формула гаммы/лада в традиционной нотации (например, ["1", "2", "b3", "4", "5", "b6", "b7"] для минора)
export interface ScaleFormula {
  name: string;           // Название (например, "Мажор", "Минор")
  steps: FormulaStep[];   // Ступени с альтерациями в виде строк
}

// ======== ГИТАРНЫЕ ТИПЫ ========

// Номер струны (от 1 до 6, где 1 - самая тонкая)
export type StringNumber = 1 | 2 | 3 | 4 | 5 | 6;

// Номер лада (от 0 до 24)
export type FretNumber = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 |
                         13 | 14 | 15 | 16 | 17 | 18 | 19 | 20 | 21 | 22 | 23 | 24;

// Струна гитары
export interface GuitarString {
  number: StringNumber;   // Номер струны
  openNote: Note;         // Нота открытой струны
}

// Позиция на грифе гитары
export interface FretboardPosition {
  string: StringNumber;   // Номер струны
  fret: FretNumber;       // Номер лада
}

// Аппликатура аккорда
export interface ChordShape {
  positions: FretboardPosition[];  // Позиции всех нот аккорда
  rootPosition: FretboardPosition; // Позиция корневой ноты
  baseFret: FretNumber;            // Базовый лад (для баррэ)
}

// ======== КОНСТАНТЫ ========

// Хроматический звукоряд (12 полутонов)
export const CHROMATIC_SCALE: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

// Стандартные интервалы
export const INTERVALS: Record<string, Interval> = {
  'P1': { name: 'Perfect Unison', shortName: 'P1', semitones: 0, quality: 'perfect', number: 1 },
  'm2': { name: 'Minor Second', shortName: 'm2', semitones: 1, quality: 'minor', number: 2 },
  'M2': { name: 'Major Second', shortName: 'M2', semitones: 2, quality: 'major', number: 2 },
  'm3': { name: 'Minor Third', shortName: 'm3', semitones: 3, quality: 'minor', number: 3 },
  'M3': { name: 'Major Third', shortName: 'M3', semitones: 4, quality: 'major', number: 3 },
  'P4': { name: 'Perfect Fourth', shortName: 'P4', semitones: 5, quality: 'perfect', number: 4 },
  'A4': { name: 'Augmented Fourth', shortName: 'A4', semitones: 6, quality: 'augmented', number: 4 },
  'd5': { name: 'Diminished Fifth', shortName: 'd5', semitones: 6, quality: 'diminished', number: 5 },
  'P5': { name: 'Perfect Fifth', shortName: 'P5', semitones: 7, quality: 'perfect', number: 5 },
  'm6': { name: 'Minor Sixth', shortName: 'm6', semitones: 8, quality: 'minor', number: 6 },
  'M6': { name: 'Major Sixth', shortName: 'M6', semitones: 9, quality: 'major', number: 6 },
  'm7': { name: 'Minor Seventh', shortName: 'm7', semitones: 10, quality: 'minor', number: 7 },
  'M7': { name: 'Major Seventh', shortName: 'M7', semitones: 11, quality: 'major', number: 7 },
  'P8': { name: 'Perfect Octave', shortName: 'P8', semitones: 12, quality: 'perfect', number: 8 }
};

// Формулы аккордов в традиционной нотации
export const CHORD_FORMULAS: Record<string, ChordFormula> = {
  'major': { name: 'Major', steps: ['1', '3', '5'], symbol: '' },
  'minor': { name: 'Minor', steps: ['1', 'b3', '5'], symbol: 'm' },
  'dim': { name: 'Diminished', steps: ['1', 'b3', 'b5'], symbol: 'dim' },
  'aug': { name: 'Augmented', steps: ['1', '3', '#5'], symbol: 'aug' },
  'sus2': { name: 'Sus2', steps: ['1', '2', '5'], symbol: 'sus2' },
  'sus4': { name: 'Sus4', steps: ['1', '4', '5'], symbol: 'sus4' },
  '7': { name: 'Dominant 7th', steps: ['1', '3', '5', 'b7'], symbol: '7' },
  'maj7': { name: 'Major 7th', steps: ['1', '3', '5', '7'], symbol: 'maj7' },
  'm7': { name: 'Minor 7th', steps: ['1', 'b3', '5', 'b7'], symbol: 'm7' },
  'm7b5': { name: 'Half-Diminished 7th', steps: ['1', 'b3', 'b5', 'b7'], symbol: 'm7b5' },
  'dim7': { name: 'Diminished 7th', steps: ['1', 'b3', 'b5', 'bb7'], symbol: 'dim7' },
  '9': { name: 'Dominant 9th', steps: ['1', '3', '5', 'b7', '9'], symbol: '9' },
  'maj9': { name: 'Major 9th', steps: ['1', '3', '5', '7', '9'], symbol: 'maj9' },
  'm9': { name: 'Minor 9th', steps: ['1', 'b3', '5', 'b7', '9'], symbol: 'm9' }
};

// Формулы гамм/ладов в традиционной нотации
export const SCALE_FORMULAS: Record<string, ScaleFormula> = {
  'major': { name: 'Major', steps: ['1', '2', '3', '4', '5', '6', '7'] },
  'minor': { name: 'Natural Minor', steps: ['1', '2', 'b3', '4', '5', 'b6', 'b7'] },
  'harmonic_minor': { name: 'Harmonic Minor', steps: ['1', '2', 'b3', '4', '5', 'b6', '7'] },
  'melodic_minor': { name: 'Melodic Minor', steps: ['1', '2', 'b3', '4', '5', '6', '7'] },
  'dorian': { name: 'Dorian', steps: ['1', '2', 'b3', '4', '5', '6', 'b7'] },
  'phrygian': { name: 'Phrygian', steps: ['1', 'b2', 'b3', '4', '5', 'b6', 'b7'] },
  'lydian': { name: 'Lydian', steps: ['1', '2', '3', '#4', '5', '6', '7'] },
  'mixolydian': { name: 'Mixolydian', steps: ['1', '2', '3', '4', '5', '6', 'b7'] },
  'locrian': { name: 'Locrian', steps: ['1', 'b2', 'b3', '4', 'b5', 'b6', 'b7'] },
  'pentatonic_major': { name: 'Major Pentatonic', steps: ['1', '2', '3', '5', '6'] },
  'pentatonic_minor': { name: 'Minor Pentatonic', steps: ['1', 'b3', '4', '5', 'b7'] },
  'blues': { name: 'Blues', steps: ['1', 'b3', '4', '#4', '5', 'b7'] }
};

// Стандартный строй гитары (от 6-й струны к 1-й)
export const STANDARD_TUNING: GuitarString[] = [
  { number: 6, openNote: { name: 'E', octave: 2 } },
  { number: 5, openNote: { name: 'A', octave: 2 } },
  { number: 4, openNote: { name: 'D', octave: 3 } },
  { number: 3, openNote: { name: 'G', octave: 3 } },
  { number: 2, openNote: { name: 'B', octave: 3 } },
  { number: 1, openNote: { name: 'E', octave: 4 } }
];

// ======== УТИЛИТЫ ДЛЯ РАБОТЫ С НОТАМИ ========

// Транспонирует ноту на заданное количество полутонов
export function transposeNote(note: Note, semitones: number): Note {
  // Находим индекс ноты в хроматическом звукоряде
  const noteIndex = CHROMATIC_SCALE.indexOf(note.name);
  if (noteIndex === -1) {
    throw new Error(`Неизвестная нота: ${note.name}`);
  }

  // Вычисляем новый индекс с учетом транспонирования
  let newIndex = (noteIndex + semitones) % 12;
  if (newIndex < 0) newIndex += 12;

  // Вычисляем изменение октавы
  let octaveChange = Math.floor((noteIndex + semitones) / 12);
  if (semitones < 0 && (noteIndex + semitones) % 12 !== 0) {
    octaveChange -= 1;
  }

  // Создаем новую ноту
  const newNote: Note = {
    name: CHROMATIC_SCALE[newIndex],
    accidental: note.accidental
  };

  // Добавляем октаву, если она была указана в исходной ноте
  if (note.octave !== undefined) {
    newNote.octave = (note.octave + octaveChange) as Octave;
  }

  return newNote;
}

// Вычисляет интервал между двумя нотами
export function getInterval(note1: Note, note2: Note): Interval | undefined {
  if (note1.octave === undefined || note2.octave === undefined) {
    throw new Error('Для вычисления интервала необходимо указать октавы обеих нот');
  }

  // Вычисляем MIDI-номера нот
  const midi1 = noteToMidi(note1);
  const midi2 = noteToMidi(note2);

  // Вычисляем разницу в полутонах
  const semitones = Math.abs(midi2 - midi1);

  // Находим интервал по количеству полутонов
  return Object.values(INTERVALS).find(interval => interval.semitones === semitones);
}

// Строит аккорд от заданной ноты по формуле
export function buildChord(root: Note, formula: ChordFormula): Note[] {
  // Вычисляем полутоны из ступеней
  const semitones = stepsToSemitones(formula.steps);

  // Строим аккорд, транспонируя корневую ноту на каждый полутон
  return semitones.map(semitone => transposeNote(root, semitone));
}

// Строит гамму от заданной ноты по формуле
export function buildScale(root: Note, formula: ScaleFormula): Note[] {
  // Вычисляем полутоны из ступеней
  const semitones = stepsToSemitones(formula.steps);

  // Строим гамму, транспонируя корневую ноту на каждый полутон
  return semitones.map(semitone => transposeNote(root, semitone));
}

// Преобразует степени с альтерациями в полутоны
// Это вспомогательная функция, которая может быть полезна для создания новых формул
export function stepsToSemitones(steps: FormulaStep[]): number[] {
  // Базовые значения для каждой степени (без альтераций)
  const baseValues: Record<string, number> = {
    '1': 0,
    '2': 2,
    '3': 4,
    '4': 5,
    '5': 7,
    '6': 9,
    '7': 11,
    '9': 14,  // 9 = 2 + октава
    '11': 17, // 11 = 4 + октава
    '13': 21  // 13 = 6 + октава
  };

  // Преобразуем каждую степень в полутоны
  return steps.map(step => {
    // Разбираем строку на степень и альтерацию
    let degree = step;
    let alteration = '';

    // Проверяем наличие альтераций
    if (step.startsWith('b') || step.startsWith('#')) {
      alteration = step.charAt(0);
      degree = step.substring(1);
    } else if (step.startsWith('bb') || step.startsWith('##')) {
      alteration = step.substring(0, 2);
      degree = step.substring(2);
    }

    let semitones = baseValues[degree];

    // Применяем альтерации
    switch (alteration) {
      case 'b': semitones -= 1; break;
      case '#': semitones += 1; break;
      case 'bb': semitones -= 2; break;
      case '##': semitones += 2; break;
    }

    return semitones;
  });
}

// Форматирует формулу аккорда или гаммы в строку
export function formatFormula(steps: FormulaStep[]): string {
  return steps.join('-');
}

// Конвертирует ноту в частоту (Гц)
// Использует формулу: f = 440 * 2^((n-69)/12), где n - MIDI-номер ноты
export function noteToFrequency(note: Note): number {
  const midiNumber = noteToMidi(note);
  return 440 * Math.pow(2, (midiNumber - 69) / 12);
}

// Конвертирует ноту в MIDI-номер
export function noteToMidi(note: Note): number {
  if (note.octave === undefined) {
    throw new Error('Для конвертации в MIDI необходимо указать октаву ноты');
  }

  const noteIndex = CHROMATIC_SCALE.indexOf(note.name);
  if (noteIndex === -1) {
    throw new Error(`Неизвестная нота: ${note.name}`);
  }

  return noteIndex + (note.octave + 1) * 12;
}

// Конвертирует MIDI-номер в ноту
export function midiToNote(midi: number): Note {
  const octave = Math.floor(midi / 12) - 1;
  const noteIndex = midi % 12;

  return {
    name: CHROMATIC_SCALE[noteIndex],
    octave: octave as Octave
  };
}

// ======== УТИЛИТЫ ДЛЯ РАБОТЫ С ГИТАРОЙ ========

// Получает ноту на заданной позиции грифа
export function getNoteAtPosition(position: FretboardPosition, tuning = STANDARD_TUNING): Note {
  // Находим струну
  const string = tuning.find(s => s.number === position.string);
  if (!string) {
    throw new Error(`Неизвестная струна: ${position.string}`);
  }

  // Транспонируем открытую ноту на количество ладов
  return transposeNote(string.openNote, position.fret);
}

// Находит все позиции заданной ноты на грифе
export function findNotePositions(note: Note, tuning = STANDARD_TUNING): FretboardPosition[] {
  const positions: FretboardPosition[] = [];

  // Для каждой струны
  tuning.forEach(string => {
    // Для каждого лада (ограничимся 24 ладами)
    for (let fret = 0; fret <= 24; fret++) {
      const fretNote = getNoteAtPosition({ string: string.number, fret: fret as FretNumber }, tuning);

      // Если имя ноты совпадает, добавляем позицию
      if (fretNote.name === note.name) {
        // Если указана октава, проверяем и её
        if (note.octave === undefined || fretNote.octave === note.octave) {
          positions.push({ string: string.number, fret: fret as FretNumber });
        }
      }
    }
  });

  return positions;
}

// Находит аппликатуры аккорда на грифе
// Это упрощенная версия, которая находит только базовые аппликатуры

export function getChordPositions(chord: Note[], tuning = STANDARD_TUNING): ChordShape[] {
  // Это сложная функция, которая требует алгоритмов поиска аппликатур
  // Здесь представлена упрощенная версия для примера

  // Находим все позиции для каждой ноты аккорда
  const notePositions = chord.map(note => ({
    note,
    positions: findNotePositions(note, tuning)
  }));

  // Находим позиции корневой ноты (первой ноты в аккорде)
  const rootPositions = notePositions[0].positions;

  // Создаем аппликатуры для каждой позиции корневой ноты
  const shapes: ChordShape[] = [];

  rootPositions.forEach(rootPosition => {
    // Базовый лад - это лад корневой ноты
    const baseFret = rootPosition.fret;

    // Находим ближайшие позиции для остальных нот аккорда
    const chordPositions: FretboardPosition[] = [rootPosition];

    // Для каждой ноты аккорда (кроме корневой)
    for (let i = 1; i < notePositions.length; i++) {
      const notePos = notePositions[i];

      // Находим ближайшую позицию к корневой ноте
      let closestPosition: FretboardPosition | null = null;
      let minDistance = Infinity;

      notePos.positions.forEach(pos => {
        // Вычисляем "расстояние" на грифе
        const distance = Math.abs(pos.fret - baseFret) +
                         Math.abs(pos.string - rootPosition.string);

        // Если это ближайшая позиция, запоминаем её
        if (distance < minDistance) {
          minDistance = distance;
          closestPosition = pos;
        }
      });

      // Добавляем ближайшую позицию
      if (closestPosition) {
        chordPositions.push(closestPosition);
      }
    }

    // Создаем аппликатуру
    shapes.push({
      positions: chordPositions,
      rootPosition,
      baseFret
    });
  });

  return shapes;
}

// ======== ДОПОЛНИТЕЛЬНЫЕ УТИЛИТЫ ========

// Форматирует название аккорда
export function formatChordName(root: Note, formula: ChordFormula): string {
  return `${root.name}${formula.symbol}`;
}

// Форматирует название интервала
export function formatIntervalName(interval: Interval): string {
  return `${interval.name} (${interval.shortName})`;
}

// Проверяет, является ли нота частью аккорда
export function isNoteInChord(note: Note, chord: Note[]): boolean {
  return chord.some(chordNote => chordNote.name === note.name);
}

// Проверяет, является ли нота частью гаммы
export function isNoteInScale(note: Note, scale: Note[]): boolean {
  return scale.some(scaleNote => scaleNote.name === note.name);
}
