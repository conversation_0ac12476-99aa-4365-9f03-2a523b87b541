import * as React from "react"
import { cn } from "@/shared/utils/utils"

interface TooltipProps {
  children: React.ReactNode
  content: string
  side?: "top" | "right" | "bottom" | "left"
  align?: "start" | "center" | "end"
  className?: string
  contentClassName?: string
}

export function Tooltip({
  children,
  content,
  side = "top",
  align = "center",
  className,
  contentClassName
}: TooltipProps) {
  const [isVisible, setIsVisible] = React.useState(false)
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const childRef = React.useRef<HTMLDivElement>(null)
  const tooltipRef = React.useRef<HTMLDivElement>(null)

  // Позиционирование подсказки
  React.useEffect(() => {
    if (isVisible && childRef.current && tooltipRef.current) {
      const childRect = childRef.current.getBoundingClientRect()
      const tooltipRect = tooltipRef.current.getBoundingClientRect()
      
      let x = 0
      let y = 0

      // Расчет позиции по горизонтали
      if (align === "start") {
        x = childRect.left
      } else if (align === "center") {
        x = childRect.left + (childRect.width / 2) - (tooltipRect.width / 2)
      } else if (align === "end") {
        x = childRect.right - tooltipRect.width
      }

      // Расчет позиции по вертикали
      if (side === "top") {
        y = childRect.top - tooltipRect.height - 5
      } else if (side === "bottom") {
        y = childRect.bottom + 5
      } else if (side === "left") {
        y = childRect.top + (childRect.height / 2) - (tooltipRect.height / 2)
        x = childRect.left - tooltipRect.width - 5
      } else if (side === "right") {
        y = childRect.top + (childRect.height / 2) - (tooltipRect.height / 2)
        x = childRect.right + 5
      }

      // Проверка на выход за границы экрана
      if (x < 10) x = 10
      if (y < 10) y = 10
      if (x + tooltipRect.width > window.innerWidth - 10) {
        x = window.innerWidth - tooltipRect.width - 10
      }
      if (y + tooltipRect.height > window.innerHeight - 10) {
        y = window.innerHeight - tooltipRect.height - 10
      }

      setPosition({ x, y })
    }
  }, [isVisible, side, align])

  return (
    <div
      className={cn("relative inline-block", className)}
      ref={childRef}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
      onFocus={() => setIsVisible(true)}
      onBlur={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={cn(
            "fixed z-50 px-3 py-1.5 text-xs rounded-md shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 max-w-xs",
            contentClassName
          )}
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
          }}
        >
          {content}
        </div>
      )}
    </div>
  )
}

export interface TooltipProviderProps {
  children: React.ReactNode
}

export function TooltipProvider({ children }: TooltipProviderProps) {
  return <>{children}</>
}
