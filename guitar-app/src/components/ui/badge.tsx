import * as React from "react"
import { cn } from "@/shared/utils/utils"

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "destructive" | "outline" | "success"
}

function Badge({
  className,
  variant = "default",
  ...props
}: BadgeProps) {
  return (
    <div
      data-slot="badge"
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        {
          "border-transparent bg-blue-600 text-white hover:bg-blue-700": variant === "default",
          "border-transparent bg-purple-600 text-white hover:bg-purple-700": variant === "secondary",
          "border-transparent bg-red-600 text-white hover:bg-red-700": variant === "destructive",
          "border-transparent bg-green-600 text-white hover:bg-green-700": variant === "success",
          "border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300": variant === "outline",
        },
        className
      )}
      {...props}
    />
  )
}

export { Badge }
