import * as React from "react"
import { cn } from "@/shared/utils/utils"

export interface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  onCheckedChange?: (checked: boolean) => void;
}

function Switch({ className, checked, onCheckedChange, onChange, ...props }: SwitchProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
    if (onCheckedChange) {
      onCheckedChange(e.target.checked);
    }
  };

  return (
    <label
      data-slot="switch"
      className={cn("relative inline-flex cursor-pointer items-center", className)}
    >
      <input
        type="checkbox"
        className="peer sr-only"
        checked={checked}
        onChange={handleChange}
        {...props}
      />
      <div
        className={cn(
          "h-6 w-11 rounded-full bg-gray-300 dark:bg-gray-600 transition-colors",
          "after:absolute after:left-0.5 after:top-0.5 after:h-5 after:w-5 after:rounded-full after:bg-white after:shadow-sm after:transition-transform",
          "peer-checked:bg-blue-600 dark:peer-checked:bg-blue-500 peer-checked:after:translate-x-full",
          "peer-focus-visible:ring-1 peer-focus-visible:ring-blue-500 peer-focus-visible:ring-offset-1",
          "peer-disabled:cursor-not-allowed peer-disabled:opacity-50"
        )}
      />
    </label>
  )
}

export { Switch }
