import React, { useState } from 'react';
import { <PERSON>H<PERSON>, <PERSON>Setting<PERSON>, FiPlus, FiMinus, <PERSON>Check, FiX } from 'react-icons/fi';
import BackButton from '../shared/ui/back-button';
import { cn } from '../shared/utils/utils';

/**
 * Компонент с примерами использования унифицированных элементов дизайна
 */
const UIComponentsExamples: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [textareaValue, setTextareaValue] = useState('');

  return (
    <div className="min-h-screen p-8 bg-white dark:bg-gray-900 font-sans">
      {/* Кнопка "Назад" */}
      <div className="mb-8">
        <BackButton />
      </div>

      <h1 className="text-3xl font-bold mb-8 text-gray-900 dark:text-gray-100">Примеры UI компонентов</h1>

      <div className="container mx-auto max-w-6xl">
        {/* Кнопки */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Кнопки</h2>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Варианты кнопок</h3>
            </div>
            <div className="p-4 flex flex-wrap gap-4">
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md">Primary</button>
              <button className="bg-purple-600 text-white hover:bg-purple-700 px-4 py-2 rounded-md">Secondary</button>
              <button className="border border-gray-300 dark:border-gray-600 bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded-md">Outline</button>
              <button className="bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded-md">Ghost</button>
              <button className="bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-md">Success</button>
              <button className="bg-red-600 text-white hover:bg-red-700 px-4 py-2 rounded-md">Danger</button>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md opacity-50 cursor-not-allowed" disabled>Disabled</button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Размеры кнопок</h3>
            </div>
            <div className="p-4 flex flex-wrap items-center gap-4">
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 text-sm rounded-md">Small</button>
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md">Default</button>
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 text-lg rounded-md">Large</button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Круглые кнопки</h3>
            </div>
            <div className="p-4 flex flex-wrap gap-4">
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-full">Rounded</button>
              <button className="bg-purple-600 text-white hover:bg-purple-700 px-4 py-2 rounded-full">Rounded</button>
              <button className="border border-gray-300 dark:border-gray-600 bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded-full">Rounded</button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Кнопки с иконками</h3>
            </div>
            <div className="p-4 flex flex-wrap gap-4">
              <button className="bg-blue-600 text-white hover:bg-blue-700 p-2 rounded-md">
                <FiPlus className="h-5 w-5" />
              </button>
              <button className="bg-purple-600 text-white hover:bg-purple-700 p-2 rounded-md">
                <FiSettings className="h-5 w-5" />
              </button>
              <button className="border border-gray-300 dark:border-gray-600 bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-md">
                <FiHome className="h-5 w-5" />
              </button>
              <button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md inline-flex items-center">
                <FiPlus className="mr-2 h-5 w-5" /> Добавить
              </button>
              <button className="bg-red-600 text-white hover:bg-red-700 px-4 py-2 rounded-md inline-flex items-center">
                <FiX className="mr-2 h-5 w-5" /> Отмена
              </button>
              <button className="bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-md inline-flex items-center">
                <FiCheck className="mr-2 h-5 w-5" /> Сохранить
              </button>
            </div>
          </div>
        </section>

        {/* Карточки */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Карточки</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
              <div className="border-b border-gray-200 dark:border-gray-700 p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Стандартная карточка</h3>
              </div>
              <div className="p-4">
                <p className="text-base text-gray-700 dark:text-gray-300 mb-4">
                  Это стандартная карточка с заголовком, содержимым и футером.
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Дополнительная информация может быть размещена здесь.
                </p>
              </div>
              <div className="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-end">
                <button className="border border-gray-300 dark:border-gray-600 bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded-md mr-2">Отмена</button>
                <button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md">Сохранить</button>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="border-b border-gray-200 dark:border-gray-700 p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Плоская карточка</h3>
              </div>
              <div className="p-4">
                <p className="text-base text-gray-700 dark:text-gray-300">
                  Это плоская карточка без тени, которая может использоваться для менее важного содержимого.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 hover:-translate-y-1 transition-transform">
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Интерактивная карточка</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Эта карточка реагирует на наведение курсора, поднимаясь вверх и увеличивая тень.
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 hover:-translate-y-1 transition-transform">
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Интерактивная карточка</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Эта карточка реагирует на наведение курсора, поднимаясь вверх и увеличивая тень.
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 hover:-translate-y-1 transition-transform">
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Интерактивная карточка</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Эта карточка реагирует на наведение курсора, поднимаясь вверх и увеличивая тень.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Формы */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Формы</h2>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
            <div className="border-b border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Элементы форм</h3>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Стандартный инпут</label>
                    <input
                      type="text"
                      className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Введите текст"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                    />
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 block">Подсказка для поля ввода</span>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Размеры инпутов</label>
                    <div className="flex flex-col gap-2">
                      <input type="text" className="w-full h-8 px-3 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Маленький" />
                      <input type="text" className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Стандартный" />
                      <input type="text" className="w-full h-12 px-3 py-3 text-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Большой" />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Состояния инпутов</label>
                    <div className="flex flex-col gap-2">
                      <input type="text" className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Обычное состояние" />
                      <input type="text" className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="Заполненное поле" readOnly />
                      <input type="text" className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 opacity-50 cursor-not-allowed" placeholder="Отключенное поле" disabled />
                    </div>
                  </div>
                </div>

                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Текстовая область</label>
                    <textarea
                      className="w-full px-3 py-2 text-base bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Введите текст"
                      rows={4}
                      value={textareaValue}
                      onChange={(e) => setTextareaValue(e.target.value)}
                    ></textarea>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Поле с ошибкой</label>
                    <input type="text" className="w-full h-10 px-3 py-2 text-base bg-white dark:bg-gray-800 border border-red-500 dark:border-red-500 rounded-md text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500" placeholder="Введите текст" />
                    <span className="text-xs text-red-500 dark:text-red-400 mt-1 block">Это поле обязательно для заполнения</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Бейджи */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Бейджи</h2>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
            <div className="border-b border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Варианты бейджей</h3>
            </div>
            <div className="p-4 flex flex-wrap gap-2">
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100">Default</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-700 dark:text-purple-100">Secondary</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100">Success</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100">Error</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-700 dark:text-yellow-100">Warning</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100">Info</span>
              <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border border-gray-200 text-gray-800 dark:border-gray-700 dark:text-gray-100">Outline</span>
            </div>
          </div>
        </section>

        {/* Статистика */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Статистика</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Всего нот</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">1,234</p>
            </div>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Точность</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">87%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Скорость</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">42 нот/мин</p>
            </div>
          </div>
        </section>

        {/* Разделители */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Разделители</h2>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
            <div className="p-4">
              <p className="text-base text-gray-700 dark:text-gray-300 mb-4">Текст перед горизонтальным разделителем</p>
              <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>
              <p className="text-base text-gray-700 dark:text-gray-300 mt-4">Текст после горизонтального разделителя</p>

              <div className="flex items-center mt-8">
                <div className="w-1/3">
                  <p className="text-base text-gray-700 dark:text-gray-300">Левая колонка</p>
                </div>
                <div className="border-r border-gray-200 dark:border-gray-700 h-16"></div>
                <div className="w-1/3 px-4">
                  <p className="text-base text-gray-700 dark:text-gray-300">Центральная колонка</p>
                </div>
                <div className="border-r border-gray-200 dark:border-gray-700 h-16"></div>
                <div className="w-1/3">
                  <p className="text-base text-gray-700 dark:text-gray-300">Правая колонка</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default UIComponentsExamples;
