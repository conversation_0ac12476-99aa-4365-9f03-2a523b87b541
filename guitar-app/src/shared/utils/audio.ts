/**
 * Утилиты для работы со звуком
 */
import { AudioOptions } from '../types/audio.types';
import { BASE_FREQUENCIES } from '../../constants/musicTheory';

/**
 * Воспроизвести аккорд по заданному паттерну
 * @param chordPattern - Паттерн аккорда в формате "X32010"
 * @param isSoundEnabled - Флаг включения/выключения звука
 * @param options - Дополнительные опции воспроизведения
 */
export function playChord(
  chordPattern: string,
  isSoundEnabled: boolean = true,
  options: AudioOptions = {}
): void {
  if (!isSoundEnabled) return;

  const {
    delayBetweenNotes = 50,
    oscillatorType = 'triangle',
    attackTime = 0.02,
    releaseTime = 2.5,
    volume = 0.2
  } = options;

  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Рассчитываем частоты для каждой струны
    const frequencies = chordPattern.split('').map((fret, index) => {
      if (fret === 'X') return 0;
      return BASE_FREQUENCIES[index] * Math.pow(2, parseInt(fret) / 12);
    }).filter(f => f > 0);

    // Воспроизводим каждую ноту с небольшой задержкой
    frequencies.forEach((frequency, index) => {
      setTimeout(() => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.type = oscillatorType;
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + attackTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + releaseTime);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + releaseTime);
      }, index * delayBetweenNotes);
    });
  } catch (error) {
    console.error('Ошибка воспроизведения звука:', error);
  }
}

/**
 * Воспроизвести одиночный звук заданной частоты
 * @param frequency - Частота звука в Гц
 * @param duration - Длительность звука в секундах
 * @param volume - Громкость звука (от 0 до 1)
 */
export function playSound(
  frequency: number,
  duration: number = 0.3,
  volume: number = 0.2
): void {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);

    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + duration);
  } catch (error) {
    console.error('Ошибка воспроизведения звука:', error);
  }
}

/**
 * Проверить, поддерживается ли Web Audio API в браузере
 */
export function isAudioSupported(): boolean {
  return !!(window.AudioContext || (window as any).webkitAudioContext);
}
