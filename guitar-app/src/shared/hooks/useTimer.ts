import { useState, useEffect, useCallback } from 'react';

interface UseTimerOptions {
  initialDuration?: number;
  initialInterval?: number;
  autoStart?: boolean;
  onComplete?: () => void;
  onIntervalComplete?: () => void;
}

export function useTimer({
  initialDuration = 5 * 60, // 5 минут по умолчанию
  initialInterval = 10, // 10 секунд по умолчанию
  autoStart = false,
  onComplete,
  onIntervalComplete,
}: UseTimerOptions = {}) {
  const [isPlaying, setIsPlaying] = useState(autoStart);
  const [duration, setDuration] = useState(initialDuration);
  const [interval, setInterval] = useState(initialInterval);
  const [timeLeft, setTimeLeft] = useState(initialDuration);
  const [intervalTimeLeft, setIntervalTimeLeft] = useState(initialInterval);

  // Форматирование времени в формат MM:SS
  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60).toString().padStart(1, '0');
    const remainingSeconds = (seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${remainingSeconds}`;
  }, []);

  // Запуск/остановка таймера
  const togglePlay = useCallback(() => {
    setIsPlaying(prev => {
      if (!prev) {
        // При запуске сбрасываем таймеры
        setTimeLeft(duration);
        setIntervalTimeLeft(interval);
      }
      return !prev;
    });
  }, [duration, interval]);

  // Сброс таймера
  const resetTimer = useCallback(() => {
    setTimeLeft(duration);
    setIntervalTimeLeft(interval);
    setIsPlaying(false);
  }, [duration, interval]);

  // Обновление длительности
  const updateDuration = useCallback((newDuration: number) => {
    setDuration(newDuration);
    if (!isPlaying) {
      setTimeLeft(newDuration);
    }
  }, [isPlaying]);

  // Обновление интервала
  const updateInterval = useCallback((newInterval: number) => {
    setInterval(newInterval);
    if (!isPlaying) {
      setIntervalTimeLeft(newInterval);
    }
  }, [isPlaying]);

  // Эффект для обновления таймеров
  useEffect(() => {
    let timerId: number | undefined = undefined;

    if (isPlaying) {
      timerId = window.setInterval(() => {
        setTimeLeft(prev => {
          const newTimeLeft = Math.max(0, prev - 1);
          
          // Проверяем, закончилось ли время
          if (newTimeLeft === 0 && onComplete) {
            onComplete();
          }
          
          return newTimeLeft;
        });

        setIntervalTimeLeft(prev => {
          const newIntervalTimeLeft = Math.max(0, prev - 1);
          
          // Проверяем, закончился ли интервал
          if (newIntervalTimeLeft === 0 && onIntervalComplete) {
            onIntervalComplete();
            return interval; // Сбрасываем интервал
          }
          
          return newIntervalTimeLeft;
        });
      }, 1000);
    }

    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, [isPlaying, interval, onComplete, onIntervalComplete]);

  return {
    isPlaying,
    duration,
    interval,
    timeLeft,
    intervalTimeLeft,
    togglePlay,
    resetTimer,
    updateDuration,
    updateInterval,
    formatTime,
  };
}
