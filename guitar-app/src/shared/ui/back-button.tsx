import { Link } from 'react-router-dom';

interface BackButtonProps {
  to?: string;
  className?: string;
  label?: string;
}

const BackButton = ({ to = "/", className = "", label = "← Назад" }: BackButtonProps) => (
  <Link to={to} className={`inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-full bg-blue-600 hover:bg-blue-700 text-white transition-colors ${className}`}>
    {label}
  </Link>
);

export default BackButton;
