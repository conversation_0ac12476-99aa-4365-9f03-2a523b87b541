import React, { HTMLAttributes, forwardRef } from 'react';

export interface SeparatorProps extends HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

const Separator = forwardRef<HTMLDivElement, SeparatorProps>(
  ({ className = '', orientation = 'horizontal', ...props }, ref) => {
    return (
      <div
        className={`${
          orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px'
        } bg-gray-200 ${className}`}
        ref={ref}
        {...props}
      />
    );
  }
);

Separator.displayName = 'Separator';

export { Separator };
