import React, { forwardRef, InputHTMLAttributes } from 'react';

export interface CheckboxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  className?: string;
  onCheckedChange?: (checked: boolean) => void;
}

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className = '', onCheckedChange, onChange, checked, ...props }, ref) => {

    // Обработчик изменения состояния чекбокса
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Вызываем оригинальный onChange, если он есть
      if (onChange) {
        onChange(e);
      }
      // Вызываем onCheckedChange, если он есть
      if (onCheckedChange) {
        onCheckedChange(e.target.checked);
      }
    };

    // Используем классы Tailwind
    return (
      <input
        type="checkbox"
        className={`h-4 w-4 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-blue-600 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer ${className}`}
        ref={ref}
        checked={checked}
        onChange={handleChange}
        {...props}
      />
    );
  }
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
