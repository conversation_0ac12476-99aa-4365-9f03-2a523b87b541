import React, { InputHTMLAttributes, forwardRef, useId } from 'react';

export interface SwitchProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  className?: string;
  onCheckedChange?: (checked: boolean) => void;
  id?: string;
  name?: string;
}

const Switch = forwardRef<HTMLInputElement, SwitchProps>(
  ({ className = '', checked, onCheckedChange, onChange, id, name, ...props }, ref) => {
    // Генерируем уникальный ID, если он не был предоставлен
    const generatedId = useId();
    const switchId = id || `switch-${generatedId}`;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
      if (onCheckedChange) {
        onCheckedChange(e.target.checked);
      }
    };

    return (
      <div className={`inline-flex items-center ${className}`}>
        <label className="relative inline-block h-6 w-11" htmlFor={switchId}>
          <input
            type="checkbox"
            className="peer h-0 w-0 opacity-0"
            ref={ref}
            checked={checked}
            onChange={handleChange}
            id={switchId}
            name={name || switchId}
            {...props}
          />
          <span className={`absolute inset-0 cursor-pointer rounded-full transition-colors duration-200 ${checked ? 'bg-blue-600' : 'bg-gray-300'}`}></span>
          <span className="absolute left-1 top-1 h-4 w-4 rounded-full bg-white shadow-sm transition-transform duration-200 peer-checked:translate-x-5"></span>
        </label>
      </div>
    );
  }
);

Switch.displayName = 'Switch';

export { Switch };
