import React, { forwardRef, InputHTMLAttributes } from 'react';

export interface SliderProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  className?: string;
}

const Slider = forwardRef<HTMLInputElement, SliderProps>(
  ({ className = '', disabled = false, ...props }, ref) => {
    return (
      <input
        type="range"
        className={`w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none
          focus:outline-none
          [&::-webkit-slider-thumb]:appearance-none
          [&::-webkit-slider-thumb]:h-4
          [&::-webkit-slider-thumb]:w-4
          [&::-webkit-slider-thumb]:rounded-full
          [&::-webkit-slider-thumb]:bg-blue-600
          [&::-webkit-slider-thumb]:dark:bg-blue-600
          [&::-webkit-slider-thumb]:cursor-pointer
          [&::-webkit-slider-thumb]:hover:bg-blue-700
          [&::-webkit-slider-thumb]:dark:hover:bg-blue-700

          [&::-moz-range-thumb]:appearance-none
          [&::-moz-range-thumb]:h-4
          [&::-moz-range-thumb]:w-4
          [&::-moz-range-thumb]:rounded-full
          [&::-moz-range-thumb]:bg-blue-600
          [&::-moz-range-thumb]:dark:bg-blue-600
          [&::-moz-range-thumb]:border-0
          [&::-moz-range-thumb]:cursor-pointer
          [&::-moz-range-thumb]:hover:bg-blue-700
          [&::-moz-range-thumb]:dark:hover:bg-blue-700

          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${className}`}
        disabled={disabled}
        ref={ref}
        {...props}
      />
    );
  }
);

Slider.displayName = 'Slider';

export { Slider };
