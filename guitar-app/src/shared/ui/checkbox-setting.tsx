import React from 'react';
import { Checkbox } from './checkbox';
import { Label } from './label';

interface CheckboxSettingProps {
  id: string;
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

const CheckboxSetting: React.FC<CheckboxSettingProps> = ({ id, label, checked, onChange, disabled }) => {
  return (
    <div className={`flex items-center space-x-2 ${disabled ? 'opacity-50 pointer-events-none' : ''}`}>
      <Checkbox
        id={id}
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
      />
      <Label htmlFor={id} className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
        {label}
      </Label>
    </div>
  );
};

export default CheckboxSetting;
