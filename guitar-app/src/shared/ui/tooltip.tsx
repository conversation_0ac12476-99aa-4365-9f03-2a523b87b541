import React, { useState, ReactNode, useEffect } from 'react';

interface TooltipProps {
  children: ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

export function Tooltip({
  children,
  content,
  position = 'top',
  className = ''
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  // Позиционирование подсказки
  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 translate-y-2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 -translate-x-2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 translate-x-2 ml-2',
  };

  // Стрелка подсказки
  const arrowClasses = {
    top: 'bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full border-t-white dark:border-t-gray-800 border-x-transparent border-b-transparent',
    bottom: 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-full border-b-white dark:border-b-gray-800 border-x-transparent border-t-transparent',
    left: 'right-0 top-1/2 transform translate-x-full -translate-y-1/2 border-l-white dark:border-l-gray-800 border-y-transparent border-r-transparent',
    right: 'left-0 top-1/2 transform -translate-x-full -translate-y-1/2 border-r-white dark:border-r-gray-800 border-y-transparent border-l-transparent',
  };

  // Адаптивная позиция для мобильных устройств
  // На мобильных устройствах лучше показывать подсказку снизу
  const [actualPosition, setActualPosition] = useState(position);

  // Ссылка на DOM-элемент для проверки позиции
  const tooltipRef = React.useRef<HTMLDivElement>(null);

  // Проверяем размер экрана и позицию элемента
  useEffect(() => {
    const handleResize = () => {
      // На мобильных устройствах всегда показываем снизу
      if (window.innerWidth < 640) {
        setActualPosition('bottom');
        return;
      }

      // Проверяем позицию элемента относительно окна
      const element = tooltipRef.current?.parentElement;
      if (element) {
        const rect = element.getBoundingClientRect();

        // Если элемент слишком близко к левому краю, показываем справа
        if (rect.left < 150 && position === 'left') {
          setActualPosition('right');
          return;
        }

        // Если элемент слишком близко к правому краю, показываем слева
        if (rect.right > window.innerWidth - 150 && position === 'right') {
          setActualPosition('left');
          return;
        }

        // Иначе используем заданную позицию
        setActualPosition(position);
      }
    };

    // Первоначальная проверка
    if (isVisible) {
      handleResize();
    }

    // Слушаем изменения размера экрана
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [position, isVisible]);

  return (
    <div
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
      onFocus={() => setIsVisible(true)}
      onBlur={() => setIsVisible(false)}
      ref={tooltipRef}
    >
      {children}

      {isVisible && (
        <div className={`absolute z-50 ${positionClasses[actualPosition]}`}>
          <div className="relative">
            <div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 px-3 py-2 rounded-md shadow-lg text-sm border border-gray-200 dark:border-gray-700 max-w-[250px] whitespace-normal break-words leading-tight">
              {content}
            </div>
            <div className={`absolute w-0 h-0 border-4 ${arrowClasses[actualPosition]}`}></div>
          </div>
        </div>
      )}
    </div>
  );
}
