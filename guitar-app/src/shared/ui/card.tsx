import React, { HTMLAttributes, forwardRef } from 'react';

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className = '', ...props }, ref) => {
    // Используем Tailwind для стилизации карточки
    return (
      <div
        className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 ${className}`.trim()}
        ref={ref}
        {...props}
      />
    );
  }
);

Card.displayName = 'Card';

export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className = '', ...props }, ref) => {
    // Используем Tailwind для стилизации содержимого карточки
    return (
      <div
        className={`p-4 ${className}`.trim()} // Используем Tailwind для отступов
        ref={ref}
        {...props}
      />
    );
  }
);

CardContent.displayName = 'CardContent';

export { Card, CardContent };
