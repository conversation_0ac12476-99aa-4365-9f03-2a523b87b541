import React from 'react';
import { GameStatistics } from '../types';

interface StatisticsDisplayProps {
  statistics: GameStatistics;
  showSpeed?: boolean;
  speedLabel?: string;
  className?: string;
}

export default function StatisticsDisplay({
  statistics,
  showSpeed = true,
  speedLabel = 'акк/мин',
  className = '',
}: StatisticsDisplayProps) {
  const { score, total, accuracy, speed, time } = statistics;
  
  // Форматирование времени в формат MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60).toString().padStart(1, '0');
    const remainingSeconds = (seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${remainingSeconds}`;
  };

  return (
    <div className={`flex items-center justify-between w-full ${className}`}>
      <div className="flex items-center">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-1">Счет:</span>
        <span className="text-sm font-bold text-gray-900 dark:text-gray-100 mr-3">
          {score}:{total} {accuracy}%
        </span>
      </div>
      
      {showSpeed && (
        <div className="flex items-center">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-1">
            <span className="inline-block w-5 h-5">⚡</span>
          </span>
          <span className="text-sm font-bold text-gray-900 dark:text-gray-100 mr-3">
            {speed} {speedLabel}
          </span>
        </div>
      )}
      
      <div className="flex items-center">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-1">
          <span className="inline-block w-5 h-5">⏱️</span>
        </span>
        <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
          {formatTime(time)}
        </span>
      </div>
    </div>
  );
}
