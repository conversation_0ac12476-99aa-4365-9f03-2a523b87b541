import React, { memo, useCallback } from 'react';
import { INLAY_FRETS } from '../../constants/musicTheory';

// Конфигурация по умолчанию
const DEFAULT_CONFIG = {
  STRINGS: 6, // 6 струн, нумерация от 1 до 6, где 1 - высокая E, 6 - низкая E
  FRETS: 12,
  MARKERS: INLAY_FRETS, // [3, 5, 7, 9, 12]
};

// Типы для отображения нот
export type NoteDisplay = {
  id: string;
  string: number; // Номер струны (1-6, где 1 - высокая E, 6 - низкая E)
  fret: number;
  label?: string;
  color?: string;
  isCorrect?: boolean;
  isRoot?: boolean;
  isHighlighted?: boolean;
};

// Интерфейс пропсов компонента
export interface GuitarFretboardProps {
  // Основные настройки
  strings?: number;
  frets?: number;
  markers?: number[];

  // Отображение
  highlightedString?: number; // Подсвеченная струна (1-6)
  // highlightedFret?: number; // Удаляем, так как подсветка теперь по нотам
  animate?: boolean; // Анимация появления

  // Ноты и взаимодействие
  notes?: NoteDisplay[]; // Отображаемые ноты
  onFretClick?: (stringNumber: number, fretIndex: number) => void; // Обработчик клика (stringNumber: 1-6)

  // Стилизация
  className?: string;
  fretboardColor?: string;
  stringColor?: string;
  fretColor?: string;
  markerColor?: string;
  highlightColor?: string;
}

/**
 * Универсальный компонент грифа гитары
 * Может использоваться в различных приложениях с разными настройками
 */
const GuitarFretboard: React.FC<GuitarFretboardProps> = memo(({
  // Применяем значения по умолчанию
  frets = DEFAULT_CONFIG.FRETS,
  markers = DEFAULT_CONFIG.MARKERS,
  highlightedString,
  animate = false,
  notes = [],
  onFretClick,
  className = '',
  fretboardColor = 'bg-yellow-100 dark:bg-yellow-900',
  stringColor = 'bg-yellow-700 dark:bg-yellow-600',
  fretColor = 'bg-yellow-700 dark:bg-yellow-600',
  markerColor = 'bg-yellow-700 dark:bg-yellow-600',
  highlightColor = 'bg-yellow-300 dark:bg-yellow-700' // Удаляем highlightedFret и highlightColor из деструктуризации, если он больше не нужен для струн
  // highlightedFret // Удаляем highlightedFret
}) => {
  // Создаем массив ладов от 0 до 12
  // 0-й лад (открытые струны) будет слева, 12-й лад будет справа
  const fretNumbers = [...Array(frets + 1)].map((_, i) => i);

  // Создаем массив струн от 1 до 6 (высокая E до низкая E)
  // 1-я струна (высокая E) будет вверху, 6-я струна (низкая E) будет внизу
  const stringNumbers = [1, 2, 3, 4, 5, 6];

  /**
   * Рендерит отдельный лад на конкретной струне
   * @param stringNumber - Номер струны (1-6, где 1 - высокая E, 6 - низкая E)
   * @param fretIndex - Индекс лада
   */
  const renderFret = useCallback((stringNumber: number, fretIndex: number) => {
    // Находим ноту для этой позиции, если она есть
    // Номер струны в данных (1-6) теперь совпадает с номером строки в отображении (1-6)
    const note = notes.find(n => n.string === stringNumber && n.fret === fretIndex);

    // Определяем, нажат ли этот лад
    const isClicked = note !== undefined;

    return (
      <div
        key={`${stringNumber}-${fretIndex}`}
        className="relative flex items-center justify-center cursor-pointer w-[calc(100%/13)] h-full"
        onClick={() => onFretClick && onFretClick(stringNumber, fretIndex)}
      >
        {/* Лад (толще на нулевом ладу) */}
        <div
          className={`absolute top-0 bottom-0 right-0 z-30 ${fretIndex === 0 ? 'w-1' : 'w-px'} ${fretColor}`}
        />

        {/* Область клика / Отображение ноты */}
        {(isClicked || onFretClick) && (
          <div
            className={`absolute w-2/3 aspect-square flex items-center justify-center rounded-full z-50 transition-all duration-300
              ${isClicked
                ? note?.isHighlighted // Проверяем флаг подсветки
                  ? 'bg-orange-500 text-white ring-2 ring-orange-300 animate-pulse' // Стиль для подсвеченной ноты
                  : note?.isCorrect !== undefined
                    ? note.isCorrect
                      ? 'bg-green-600 text-white animate-pulse-green'
                      : 'bg-red-600 text-white animate-pulse-red'
                    : note?.isRoot
                      ? 'bg-green-600 text-white'
                      : note?.color || 'bg-blue-600 text-white'
                : 'bg-yellow-50 dark:bg-yellow-800 border border-yellow-400 dark:border-yellow-500 opacity-0 hover:opacity-100'
              }`}
          >
            {/* Отображение текста ноты */}
            {isClicked && note?.label && (
              <span className="text-[0.7em] leading-none p-[0.25em] font-bold">
                {note.label}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }, [notes, onFretClick, fretColor]);

  /**
   * Рендерит маркеры ладов (точки)
   */
  const renderFretMarkers = useCallback(() => (
    <div className="flex items-center mb-4 md:mb-6 w-full">
      {fretNumbers.map((fretIndex) => (
        <div
          key={fretIndex}
          className="flex items-center justify-center w-[calc(100%/13)]"
          style={{ height: 'clamp(0.5rem, 1.25vw, 1rem)' }}
        >
          {markers.includes(fretIndex) && (
            <div className="flex">
              <div
                className={`rounded-full ${markerColor}`}
                style={{
                  width: 'clamp(0.25rem, 0.6vw, 0.5rem)',
                  height: 'clamp(0.25rem, 0.6vw, 0.5rem)',
                  marginRight: fretIndex === 12 ? 'clamp(0.125rem, 0.3vw, 0.25rem)' : '0'
                }}
              />
              {/* Двойная точка на 12 ладу */}
              {fretIndex === 12 && (
                <div
                  className={`rounded-full ${markerColor}`}
                  style={{
                    width: 'clamp(0.25rem, 0.6vw, 0.5rem)',
                    height: 'clamp(0.25rem, 0.6vw, 0.5rem)'
                  }}
                />
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  ), [fretNumbers, markers, markerColor]);

  return (
    <div className={`w-full ${className}`}>
      {/* Гриф */}
      <div className={`flex flex-col ${fretboardColor} border-2 border-yellow-700 dark:border-yellow-600 rounded-lg shadow-lg relative w-full overflow-hidden`}>
        {/* Удаляем блок подсветки всего лада */}
        {/* {highlightedFret !== undefined && (...)} */}

        {stringNumbers.map((stringNumber) => (
          <div
            key={stringNumber}
            className="flex items-center relative"
            style={{ height: 'clamp(1.5rem, 2.5vw, 2.25rem)' }}
          >
            {/* Подсветка целевой струны (отображается поверх подсветки лада) */}
            {highlightedString === stringNumber && (
              <div
                className={`absolute inset-x-0 top-[20%] h-[60%] ${highlightColor} z-20 ${animate ? 'animate-pop-up' : ''}`}
              />
            )}

            {/* Линия струны */}
            <div
              className={`absolute left-0 right-0 top-1/2 h-px ${stringColor} z-40`}
            />

            {/* Лады */}
            {fretNumbers.map((fretIndex) => renderFret(stringNumber, fretIndex))}
          </div>
        ))}
      </div>

      {/* Маркеры ладов */}
      {renderFretMarkers()}
    </div>
  );
});

export default GuitarFretboard;
