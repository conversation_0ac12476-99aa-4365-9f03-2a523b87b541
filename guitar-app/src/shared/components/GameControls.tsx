import React from 'react';
import { Shuffle } from 'lucide-react';

interface GameControlsProps {
  isPlaying: boolean;
  isRandomPlay: boolean;
  onTogglePlay: () => void;
  onToggleRandomPlay: () => void;
  disabled?: boolean;
  className?: string;
}

export default function GameControls({
  isPlaying,
  isRandomPlay,
  onTogglePlay,
  onToggleRandomPlay,
  disabled = false,
  className = '',
}: GameControlsProps) {
  return (
    <div className={`flex w-full gap-2 ${className}`}>
      <button
        onClick={onTogglePlay}
        disabled={disabled}
        className={`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-1 focus:ring-opacity-50 focus:ring-offset-0 h-12 md:h-14 text-base md:text-lg px-6 flex-1 border transform-none transition-colors ${
          isPlaying
            ? 'bg-red-600 hover:bg-red-700 active:bg-red-700 text-white border-red-600 dark:border-red-600 focus:ring-red-500'
            : 'bg-green-600 hover:bg-green-700 active:bg-green-700 text-white border-green-600 dark:border-green-600 focus:ring-green-500'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        aria-label={isPlaying ? "Остановить игру" : "Начать игру"}
      >
        {isPlaying ? 'СТОП' : 'СТАРТ'}
      </button>
      <button
        onClick={onToggleRandomPlay}
        className={`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-1 focus:ring-opacity-50 focus:ring-offset-0 h-12 md:h-14 aspect-square border transform-none transition-colors ${
          isRandomPlay
            ? 'bg-blue-600 hover:bg-blue-700 active:bg-blue-700 text-white border-blue-600 dark:border-blue-600 focus:ring-blue-500'
            : 'bg-white dark:bg-gray-800 hover:bg-gray-100 active:bg-gray-100 dark:hover:bg-gray-700 dark:active:bg-gray-700 border-gray-300 dark:border-gray-600 focus:ring-gray-500'
        }`}
        aria-label={isRandomPlay ? "Отключить случайное воспроизведение" : "Включить случайное воспроизведение"}
        title={isRandomPlay ? "Отключить случайное воспроизведение" : "Включить случайное воспроизведение"}
      >
        <Shuffle className="h-6 w-6" />
      </button>
    </div>
  );
}
