import React from 'react';
import { Slider } from '../ui/slider';

interface TimerControlProps {
  duration: number;
  interval: number;
  timeLeft: number;
  isPlaying: boolean;
  onDurationChange: (value: number) => void;
  onIntervalChange: (value: number) => void;
  formatTime: (seconds: number) => string;
  className?: string;
}

export default function TimerControl({
  duration,
  interval,
  timeLeft,
  isPlaying,
  onDurationChange,
  onIntervalChange,
  formatTime,
  className = '',
}: TimerControlProps) {
  return (
    <div className={`flex flex-col space-y-3 ${className}`}>
      {/* Таймер */}
      <div className="flex items-center w-full">
        <span className="text-sm text-gray-600 dark:text-gray-400 mr-2 w-16">Таймер:</span>
        <Slider
          min={1}
          max={60}
          step={1}
          value={isPlaying ? timeLeft / 60 : duration}
          onChange={(e) => !isPlaying && onDurationChange(parseInt(e.target.value))}
          disabled={isPlaying}
          className="flex-1"
        />
        <span className="inline-flex items-center justify-center ml-2 px-1.5 py-0.5 font-bold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 text-sm w-[3.5rem] text-center font-mono">
          {isPlaying ? formatTime(timeLeft) : `${duration}:00`}
        </span>
      </div>

      {/* Интервал */}
      <div className="flex items-center w-full">
        <span className="text-sm text-gray-600 dark:text-gray-400 mr-2 w-16">Интервал:</span>
        <Slider
          value={interval}
          onChange={(e) => onIntervalChange(parseInt(e.target.value))}
          min={1}
          max={60}
          step={1}
          disabled={isPlaying}
          className="flex-1"
        />
        <span className="inline-flex items-center justify-center ml-2 px-1.5 py-0.5 font-bold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 text-sm w-[3.5rem] text-center font-mono">
          {`0:${interval.toString().padStart(2, '0')}`}
        </span>
      </div>
    </div>
  );
}
