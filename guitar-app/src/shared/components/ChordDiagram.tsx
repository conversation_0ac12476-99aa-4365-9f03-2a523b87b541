import React from 'react';

interface ChordDiagramProps {
  pattern: string;
  variation?: number;
  totalVariations?: number;
  onPlay?: (pattern: string) => void;
  size?: 'sm' | 'md' | 'lg';
  showFretNumbers?: boolean;
  className?: string;
}

/**
 * Универсальный компонент для отображения диаграммы аккорда
 */
const ChordDiagram: React.FC<ChordDiagramProps> = ({
  pattern,
  variation = 0,
  totalVariations = 1,
  onPlay,
  size = 'md',
  showFretNumbers = false,
  className = '',
}) => {
  const handleClick = () => {
    if (onPlay) {
      onPlay(pattern);
    }
  };

  // Определение размеров в зависимости от параметра size
  const sizeClasses = {
    sm: 'w-full max-w-[200px]',
    md: 'w-full max-w-[280px]',
    lg: 'w-full max-w-[350px]',
  };

  // Координаты струн (слева направо: 6, 5, 4, 3, 2, 1 струны)
  // Где 6 - низкая E, 1 - высокая E
  const stringPositions = [20, 52, 84, 116, 148, 180];

  return (
    <svg
      viewBox="0 0 200 280"
      className={`${sizeClasses[size]} ${className} ${onPlay ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
      role={onPlay ? "button" : undefined}
      aria-label={onPlay ? "Воспроизвести аккорд" : "Диаграмма аккорда"}
    >
      {/* Основная рамка грифа удалена */}

      {/* Горизонтальные линии (лады) - сделаны шире */}
      <path
        d="M20 45h160 M20 95h160 M20 145h160 M20 195h160 M20 245h160"
        stroke="currentColor"
        strokeWidth="1"
        className="transition-all duration-300 ease-in-out"
      />

      {/* Вертикальные линии (струны) */}
      <path
        d="M20 45v200 M52 45v200 M84 45v200 M116 45v200 M148 45v200 M180 45v200"
        stroke="currentColor"
        strokeWidth="1"
        className="transition-all duration-300 ease-in-out"
      />

      {/* Верхний порожек (нулевой лад) */}
      <path
        d="M20 45h160"
        stroke="currentColor"
        strokeWidth="4"
        className="transition-all duration-300 ease-in-out"
      />

      {/* Отображение аппликатуры аккорда */}
      {pattern.split('').map((fret, index) => {
        if (index >= stringPositions.length) return null; // Проверка на выход за границы массива

        const x = stringPositions[index]; // Точная координата струны

        return fret === 'X' ? (
          // Крестик для струн, которые не играются - с таким же отступом как у кружков
          <g key={index}>
            <path
              d={`M${x-10} ${45-25-10} L${x+10} ${45-25+10} M${x-10} ${45-25+10} L${x+10} ${45-25-10}`}
              stroke="currentColor"
              strokeWidth="1.5"
              className="transition-all duration-300 ease-in-out"
            />
          </g>
        ) : fret === '0' ? (
          // Кружок для открытых струн - размер как у меток на грифе
          <circle
            key={index}
            cx={x}
            cy={45 - 25}
            r="12"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            className="transition-all duration-300 ease-in-out"
          />
        ) : (
          // Точка для зажатых ладов - размещаем точно на струне между ладами
          <circle
            key={index}
            cx={x}
            cy={45 + (parseInt(fret) - 0.5) * 50}
            r="14"
            fill="currentColor"
            className="transition-all duration-300 ease-in-out"
          />
        );
      })}

      {/* Номера ладов (опционально) */}
      {showFretNumbers && (
        <>
          <text x="10" y="70" fontSize="12" fill="currentColor">1</text>
          <text x="10" y="120" fontSize="12" fill="currentColor">2</text>
          <text x="10" y="170" fontSize="12" fill="currentColor">3</text>
          <text x="10" y="220" fontSize="12" fill="currentColor">4</text>
        </>
      )}

      {/* Индикатор вариации аккорда */}
      {totalVariations > 1 && (
        <text
          x="100"
          y="270"
          textAnchor="middle"
          fontSize="16"
          fill="currentColor"
          className="transition-all duration-300 ease-in-out"
        >
          {variation + 1} из {totalVariations}
        </text>
      )}
    </svg>
  );
};

export default ChordDiagram;
