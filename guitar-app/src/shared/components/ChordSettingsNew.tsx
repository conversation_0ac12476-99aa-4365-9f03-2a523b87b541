import React from 'react';
import { X, HelpCircle, Info } from 'lucide-react';
import { CHORD_CATEGORIES, getChordsByCategory } from '../../constants/musicTheory';

// Импорт компонентов shadcn/ui
import { Dialog, DialogHeader, DialogTitle, DialogContent } from "../../components/ui/dialog";
import { ScrollArea } from "../../components/ui/scroll-area";
import { Switch } from "../../components/ui/switch";
import { Button } from "../../components/ui/button";
import { Separator } from "../../components/ui/separator";
import { Tooltip } from "../../components/ui/tooltip";

interface ChordSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  enabledCategories: Record<string, boolean>;
  enabledChords: Record<string, boolean>;
  onToggleCategory: (category: string) => void;
  onToggleChord: (chord: string) => void;
  onSelectAllCategories: (select: boolean) => void;
  onSelectAllChords: (select: boolean) => void;
  useAlternativeFingerings?: boolean;
  onToggleAlternativeFingerings?: () => void;
}

/**
 * Компонент настроек для выбора аккордов и категорий
 */
const ChordSettingsNew: React.FC<ChordSettingsProps> = ({
  open,
  onOpenChange,
  enabledCategories,
  enabledChords,
  onToggleCategory,
  onToggleChord,
  onSelectAllCategories,
  onSelectAllChords,
  useAlternativeFingerings = false,
  onToggleAlternativeFingerings
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto w-full">
        <DialogHeader className="relative">
          <DialogTitle className="text-xl md:text-2xl font-bold pr-8 text-gray-900 dark:text-gray-100">Настройки аккордов</DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[50vh] md:max-h-[60vh] pr-4">
          <div className="py-3 space-y-5">
            {/* Дополнительные настройки */}
            {onToggleAlternativeFingerings && (
              <div className="flex items-center justify-between mb-4 pb-3 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <label htmlFor="alternative-fingerings" className="text-gray-600 dark:text-gray-400 font-medium mr-2">
                    Использовать альтернативные аппликатуры
                  </label>
                  <Tooltip content="Показывать разные варианты аппликатур для одного и того же аккорда">
                    <div className="text-gray-400 dark:text-gray-500 cursor-help">
                      <HelpCircle size={16} />
                    </div>
                  </Tooltip>
                </div>
                <Switch
                  id="alternative-fingerings"
                  checked={useAlternativeFingerings}
                  onCheckedChange={onToggleAlternativeFingerings}
                />
              </div>
            )}
            {/* Кнопки выбора всех категорий */}
            <div className="flex flex-col sm:flex-row justify-between gap-2 mb-3">
            </div>

            {/* Список категорий */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-6">
              {Object.entries(CHORD_CATEGORIES).map(([id, category]) => (
                <div key={id} className="flex items-center hover:bg-gray-100 dark:hover:bg-gray-800 p-1.5 rounded-md transition-colors">
                  <Switch
                    id={`category-${id}`}
                    checked={enabledCategories[id]}
                    onCheckedChange={() => onToggleCategory(id)}
                  />
                  <label htmlFor={`category-${id}`} className="ml-2 cursor-pointer truncate">
                    {category.name}
                  </label>
                </div>
              ))}
            </div>

            <Separator className="my-3" />

            {/* Кнопки выбора всех аккордов */}
            <div className="flex flex-col sm:flex-row justify-between gap-2 mb-3">
            </div>

            {/* Список аккордов по категориям */}
            <div className="space-y-4">
              {Object.entries(CHORD_CATEGORIES).map(([categoryId, category]) => {
                const chordsInCategory = getChordsByCategory(categoryId);
                if (chordsInCategory.length === 0) return null;

                return (
                  <div key={categoryId} className={`space-y-1.5 ${!enabledCategories[categoryId] ? 'opacity-50' : ''}`}>
                    <div className="flex items-center">
                      <h3 className="font-medium">{category.name}</h3>
                      {category.description && (
                        <Tooltip content={category.description}>
                          <div className="ml-2 text-gray-400 dark:text-gray-500 cursor-help">
                            <Info size={16} />
                          </div>
                        </Tooltip>
                      )}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-1.5">
                      {chordsInCategory.map(chord => (
                        <div key={chord.name} className="flex items-center hover:bg-gray-100 dark:hover:bg-gray-800 p-1.5 rounded-md transition-colors">
                          <Switch
                            id={`chord-${chord.name}`}
                            checked={enabledChords[chord.name]}
                            onCheckedChange={() => onToggleChord(chord.name)}
                            disabled={!enabledCategories[categoryId]}
                          />
                          <label htmlFor={`chord-${chord.name}`} className="ml-2 cursor-pointer text-sm">
                            {chord.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </ScrollArea>

        <div className="mt-6 flex justify-end">
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ChordSettingsNew;
