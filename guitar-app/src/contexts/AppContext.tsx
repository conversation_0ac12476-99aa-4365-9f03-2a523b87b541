import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Chord } from '../shared/types';

// Типы для состояния
interface AppState {
  currentChord: Chord | null;
  settings: {
    isAudioEnabled: boolean;
    isRandomPlay: boolean;
    useAlternativeFingerings: boolean;
  };
  statistics: {
    score: number;
    total: number;
    accuracy: number;
    speed: number;
    time: number;
  };
  gameState: {
    isPlaying: boolean;
    startTime: number | null;
    isTimedMode: boolean;
  };

  // Состояние для GuitarNoteIntervalGame
  guitarNoteGame: {
    targetNote: string;
    targetString: number;
    targetFret: number;
    clickedFret: { string: number, fret: number } | null;
    clickedNote: string;
    isCorrect: boolean | null;
    animate: boolean;
    isIntervalMode: boolean;
  };
}

// Типы для действий
type AppAction =
  | { type: 'SET_CURRENT_CHORD'; payload: Chord | null }
  | { type: 'TOGGLE_AUDIO' }
  | { type: 'TOGGLE_RANDOM_PLAY' }
  | { type: 'TOGGLE_ALTERNATIVE_FINGERINGS' }
  | { type: 'UPDATE_STATISTICS'; payload: Partial<AppState['statistics']> }
  | { type: 'RESET_STATISTICS' }

  // Действия для управления игрой
  | { type: 'START_GAME' }
  | { type: 'STOP_GAME' }
  | { type: 'INCREMENT_SCORE' }
  | { type: 'INCREMENT_TOTAL' }
  | { type: 'UPDATE_SPEED'; payload: number }
  | { type: 'UPDATE_TIME'; payload: number }
  | { type: 'RESET_BUTTON_STATES' }
  | { type: 'TOGGLE_TIMED_MODE' }

  // Действия для GuitarNoteIntervalGame
  | { type: 'SET_TARGET_NOTE'; payload: { note: string, string: number, fret: number } }
  | { type: 'SET_CLICKED_FRET'; payload: { string: number, fret: number, note: string, isCorrect: boolean } | null }
  | { type: 'SET_ANIMATE'; payload: boolean }
  | { type: 'TOGGLE_INTERVAL_MODE'; payload: boolean };

// Начальное состояние
const initialState: AppState = {
  currentChord: null,
  settings: {
    isAudioEnabled: true,
    isRandomPlay: false,
    useAlternativeFingerings: false,
  },
  statistics: {
    score: 0,
    total: 0,
    accuracy: 0,
    speed: 0,
    time: 0,
  },
  gameState: {
    isPlaying: false,
    startTime: null,
    isTimedMode: false,
  },
  guitarNoteGame: {
    targetNote: '',
    targetString: 0,
    targetFret: 0,
    clickedFret: null,
    clickedNote: '',
    isCorrect: null,
    animate: false,
    isIntervalMode: false,
  },
};

// Редьюсер
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_CURRENT_CHORD':
      return {
        ...state,
        currentChord: action.payload,
      };
    case 'TOGGLE_AUDIO':
      return {
        ...state,
        settings: {
          ...state.settings,
          isAudioEnabled: !state.settings.isAudioEnabled,
        },
      };
    case 'TOGGLE_RANDOM_PLAY':
      return {
        ...state,
        settings: {
          ...state.settings,
          isRandomPlay: !state.settings.isRandomPlay,
        },
      };
    case 'TOGGLE_ALTERNATIVE_FINGERINGS':
      return {
        ...state,
        settings: {
          ...state.settings,
          useAlternativeFingerings: !state.settings.useAlternativeFingerings,
        },
      };
    case 'UPDATE_STATISTICS':
      return {
        ...state,
        statistics: {
          ...state.statistics,
          ...action.payload,
        },
      };
    case 'RESET_STATISTICS':
      return {
        ...state,
        statistics: {
          score: 0,
          total: 0,
          accuracy: 0,
          speed: 0,
          time: 0,
        },
      };

    // Обработка действий для управления игрой
    case 'START_GAME':
      return {
        ...state,
        gameState: {
          ...state.gameState,
          isPlaying: true,
          startTime: Date.now(),
        },
        statistics: {
          score: 0,
          total: 0,
          accuracy: 0,
          speed: 0,
          time: 0,
        },
        guitarNoteGame: {
          ...state.guitarNoteGame,
          targetNote: '',
          targetString: 0,
          targetFret: 0,
          clickedFret: null,
          clickedNote: '',
          isCorrect: null,
          // Сохраняем текущий режим (ноты или интервалы)
        },
      };

    case 'STOP_GAME':
      return {
        ...state,
        gameState: {
          ...state.gameState,
          isPlaying: false,
        },
      };

    case 'INCREMENT_SCORE':
      const newScore = state.statistics.score + 1;
      const newTotal = state.statistics.total + 1;
      const newAccuracy = Math.round((newScore / newTotal) * 100);

      return {
        ...state,
        statistics: {
          ...state.statistics,
          score: newScore,
          total: newTotal,
          accuracy: newAccuracy,
        },
      };

    case 'INCREMENT_TOTAL':
      const updatedTotal = state.statistics.total + 1;
      const updatedAccuracy = state.statistics.score > 0
        ? Math.round((state.statistics.score / updatedTotal) * 100)
        : 0;

      return {
        ...state,
        statistics: {
          ...state.statistics,
          total: updatedTotal,
          accuracy: updatedAccuracy,
        },
      };

    case 'UPDATE_SPEED':
      return {
        ...state,
        statistics: {
          ...state.statistics,
          speed: action.payload,
        },
      };

    case 'UPDATE_TIME':
      return {
        ...state,
        statistics: {
          ...state.statistics,
          time: state.statistics.time + action.payload,
        },
      };

    case 'RESET_BUTTON_STATES':
      return {
        ...state,
        guitarNoteGame: {
          ...state.guitarNoteGame,
          clickedFret: null,
          clickedNote: '',
          isCorrect: null,
        },
      };

    case 'TOGGLE_TIMED_MODE':
      return {
        ...state,
        gameState: {
          ...state.gameState,
          isTimedMode: !state.gameState.isTimedMode,
        },
      };

    // Обработка действий для GuitarNoteIntervalGame
    case 'SET_TARGET_NOTE':
      return {
        ...state,
        guitarNoteGame: {
          ...state.guitarNoteGame,
          targetNote: action.payload.note,
          targetString: action.payload.string,
          targetFret: action.payload.fret,
          animate: true,
        },
      };

    case 'SET_CLICKED_FRET':
      if (action.payload === null) {
        return {
          ...state,
          guitarNoteGame: {
            ...state.guitarNoteGame,
            clickedFret: null,
            clickedNote: '',
            isCorrect: null,
          },
        };
      }

      return {
        ...state,
        guitarNoteGame: {
          ...state.guitarNoteGame,
          clickedFret: {
            string: action.payload.string,
            fret: action.payload.fret,
          },
          clickedNote: action.payload.note,
          isCorrect: action.payload.isCorrect,
        },
      };

    case 'SET_ANIMATE':
      return {
        ...state,
        guitarNoteGame: {
          ...state.guitarNoteGame,
          animate: action.payload,
        },
      };

    case 'TOGGLE_INTERVAL_MODE':
      return {
        ...state,
        guitarNoteGame: {
          ...state.guitarNoteGame,
          isIntervalMode: action.payload,
        },
      };

    default:
      return state;
  }
}

// Создание контекста
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Провайдер контекста
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Хук для использования контекста
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}
