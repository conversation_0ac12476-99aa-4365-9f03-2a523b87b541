{"name": "guitar-fretboard-app", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "pitchy": "^4.1.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^22.15.2", "@vitejs/plugin-react": "^4.2.1", "vite": "^6.3.3"}}