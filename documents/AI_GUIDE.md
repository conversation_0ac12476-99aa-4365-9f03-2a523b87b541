# Руководство для ИИ по работе с проектом

Этот документ содержит инструкции для ИИ (Claude, GPT и других ассистентов) по работе с проектом Guitar Club Web App. Следуйте этим правилам при выполнении задач в рамках проекта.

## Общие правила

1. **Язык общения**: Используйте русский язык для общения с пользователем.

2. **Стиль кода**:
   - Пишите чистый, понятный и хорошо структурированный код
   - Следуйте принципам TypeScript с строгой типизацией
   - Избегайте типов `any` и `unknown`
   - Используйте функциональные компоненты и хуки React

3. **Документация**:
   - Добавляйте комментарии к сложным частям кода
   - Документируйте публичные функции и компоненты
   - Не создавайте избыточную документацию

4. **Коммиты**:
   - Никогда не делайте коммиты без явного указания пользователя
   - Даже если коммит повторный, дождитесь явного разрешения

5. **Подход к решению проблем**:
   - Всегда ищите корень проблемы, а не делайте быстрые исправления
   - Предлагайте долгосрочные решения, а не временные обходные пути

## Порядок работы с задачами

1. **Анализ задачи**:
   - Внимательно изучите запрос пользователя
   - Определите, какие файлы и компоненты затрагивает задача
   - Изучите существующий код с помощью инструмента codebase-retrieval

2. **Планирование**:
   - Составьте детальный план действий
   - Определите, какие файлы нужно изменить
   - Опишите, какие изменения будут внесены

3. **Реализация**:
   - Следуйте плану, внося изменения в код
   - Используйте str-replace-editor для редактирования файлов
   - Создавайте новые файлы только при необходимости

4. **Тестирование**:
   - Предложите пользователю протестировать изменения
   - Опишите, как можно проверить работоспособность
   - Будьте готовы исправить ошибки, если они возникнут

5. **Документирование**:
   - Объясните внесенные изменения
   - При необходимости обновите документацию проекта
   - Предложите дальнейшие улучшения, если они есть

## Работа с кодовой базой

1. **Перед редактированием файла**:
   - Всегда сначала используйте codebase-retrieval для получения информации о коде
   - Запрашивайте детальную информацию о всех символах, которые будут затронуты
   - Делайте это в одном запросе, а не в нескольких

2. **При редактировании файлов**:
   - Используйте str-replace-editor, а не создавайте новые файлы для замены существующих
   - Сохраняйте стиль и структуру кода
   - Будьте консервативны в изменениях

3. **При создании новых файлов**:
   - Следуйте соглашениям по именованию из DEV_GUIDE.md
   - Используйте существующие компоненты и утилиты
   - Обеспечьте совместимость с остальной кодовой базой

## Работа с UI компонентами

1. **Миграция на shadcn/ui**:
   - Создавайте новые версии компонентов с суффиксом "New"
   - Используйте компоненты из src/components/ui
   - Следуйте стилю New York и нейтральной цветовой схеме
   - Сохраняйте всю существующую функциональность

2. **Стилизация**:
   - Используйте Tailwind CSS для стилизации
   - Следуйте принципам из UI_GUIDE.md
   - Обеспечивайте адаптивность для разных устройств
   - Поддерживайте как светлую, так и темную тему

3. **Компоненты**:
   - Предпочитайте переключатели (Switch) вместо чекбоксов
   - Используйте иконки вместо текстовых меток, где это уместно
   - Центрируйте текст с равными отступами
   - Кнопка START должна быть зеленой, STOP - красной

## Работа с музыкальными данными

1. **Нотация**:
   - Используйте простые символы '#' и 'b' для диезов и бемолей в коде и юникодные символы в интерфейсе
   - Используйте заглавные буквы для обозначения аккордов
   - Формулы аккордов должны использовать традиционную нотацию с корнем как 1

2. **Гитарные компоненты**:
   - Индексация струн: от 6 до 1, с 6-й струной внизу в интерфейсе грифа
   - Корневые ноты должны отображаться как квадраты с закругленными углами

3. **Статистика**:
   - Отображайте статистику в одну строку: Счет, Точность, Скорость, Время
   - Используйте "акк/мин" вместо "нот/мин" в компоненте "Name The Chord"
   - Рассчитывайте скорость только на основе правильных ответов
   - Отображайте числовые значения без десятичных знаков

## Запуск приложения

Для запуска приложения используйте команду:
```
cd guitar-app && npm start
```

## Особые указания

1. **Не меняйте интерфейс без явного запроса пользователя**, даже если изменение кажется улучшением.

2. **Предпочитайте меньший размер кода компонентов** и меньшее количество элементов UI для улучшения поддерживаемости и пользовательского опыта.

3. **Предотвращайте прыжки интерфейса** при изменении числовых значений в статистике или при изменении таймера.

4. **Оптимизируйте код**, предпочитая решения, которые уменьшают общий размер кода, а не увеличивают его.

5. **Предпочитайте создание новых файлов** вместо модификации существующих, сохраняя старые файлы для справки.

## Заключение

Следуя этим инструкциям, вы обеспечите последовательную и качественную работу над проектом Guitar Club Web App. Всегда обращайтесь к соответствующим руководствам (DEV_GUIDE.md, UI_GUIDE.md и др.) для получения более подробной информации по конкретным аспектам проекта.
