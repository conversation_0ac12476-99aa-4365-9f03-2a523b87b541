# Дорожная карта проекта

Краткий план развития проекта Guitar Club Web App.

## Текущие задачи

### Фаза 1: Рефакторинг и оптимизация кода
- [x] Объединить все музыкальные структуры в отдельный модуль musicTheory.ts
- [ ] Оптимизировать musicTheory.ts (улучшить организацию, добавить комментарии)
- [ ] Стандартизировать нотацию: в коде везде должны быть #b, а в интерфейсе юникодные символы (♯, 𝄪, ♭, 𝄫)
  - [ ] Улучшить useTimer для всех компонентов с таймерами
  - [ ] Создать useAudio для работы со звуком
  - [ ] Создать useStatistics для обработки статистики
- [ ] Реорганизовать управление состоянием:
  - [ ] Четко определить, что хранить в глобальном/локальном состоянии
  - [ ] Оптимизировать AppContext, удалив избыточные данные
  - [ ] Стандартизировать использование useReducer для сложных состояний

### Фаза 2: Модульность и разделение ответственности
- [ ] Разделить большие компоненты на более мелкие:
  - [ ] Упростить HomePage
  - [ ] Упростить Guitar Note/Interval Game
  - [ ] Упростить Pitch Trainer
  - [ ] Упростить Note Recognition
  - [ ] Упростить Guitar Neck Editor
  - [ ] Упростить Score Counter
  - [ ] Упростить Random Chord Generator
- [ ] Отделить бизнес-логику от представления:
  - [ ] Создать отдельные файлы для логики (hooks, utils)
  - [ ] Оставить в компонентах только рендеринг и обработку событий

### Фаза 3: Миграция на shadcn/ui
- [ ] Мигрировать Guitar Note/Interval Game
- [ ] Мигрировать Pitch Trainer
- [ ] Мигрировать Note Recognition
- [ ] Мигрировать Guitar Neck Editor
- [ ] Мигрировать Score Counter
- [ ] Мигрировать Random Chord Generator
- [ ] Удалить старые версии компонентов после успешной миграции

### Фаза 4: Улучшение UI/UX
- [ ] Стандартизировать дизайн-систему:
  - [ ] Создать единый набор компонентов UI
  - [ ] Оптимизировать стили и классы Tailwind CSS
  - [ ] Стандартизировать использование иконок
- [ ] Унифицировать пользовательский интерфейс:
  - [ ] Стандартизировать отображение статистики
  - [ ] Унифицировать расположение элементов управления
  - [ ] Создать единый стиль для всех диалоговых окон
- [ ] Улучшить доступность (a11y):
  - [ ] Добавить поддержку клавиатурной навигации
  - [ ] Улучшить семантическую структуру HTML
  - [ ] Добавить ARIA-атрибуты
- [ ] Улучшить адаптивность для мобильных устройств

### Фаза 5: Оптимизация производительности
- [ ] Оптимизировать рендеринг:
  - [ ] Добавить мемоизацию (useMemo, useCallback)
  - [ ] Использовать React.memo для предотвращения лишних перерендеров
- [ ] Оптимизировать работу с аудио:
  - [ ] Улучшить обработку звука и распознавание нот
  - [ ] Добавить фильтрацию шума
  - [ ] Улучшить обработку ошибок при работе с микрофоном
- [ ] Оптимизировать загрузку ресурсов:
  - [ ] Внедрить ленивую загрузку компонентов (React.lazy)
  - [ ] Оптимизировать размер бандла

### Фаза 6: Тестирование и качество кода
- [ ] Добавить автоматические тесты:
  - [ ] Написать unit-тесты для критических функций
  - [ ] Добавить интеграционные тесты для компонентов
  - [ ] Настроить тестовое окружение
- [ ] Внедрить инструменты контроля качества:
  - [ ] Настроить ESLint для проверки кода
  - [ ] Настроить Prettier для форматирования
  - [ ] Добавить проверку типов TypeScript

## Будущие задачи

### Улучшение архитектуры
- [ ] Внедрить строгую типизацию TypeScript во всем проекте
- [ ] Создать диаграмму компонентов и зависимостей
- [ ] Настроить CI/CD для автоматизации сборки и деплоя

### Новые функции
- [ ] Добавить тренажер для изучения гамм
- [ ] Добавить тренажер для изучения музыкальной теории
- [ ] Расширить базу аккордов и нот
- [ ] Добавить сохранение прогресса пользователя

### Улучшение пользовательского опыта
- [ ] Добавить звуковые эффекты для обратной связи
- [ ] Унифицировать отображение аккордов и нот
- [ ] Добавить анимации и переходы между состояниями
- [ ] Улучшить визуализацию прогресса обучения

## Выполненные задачи

- [x] Реорганизовать структуру проекта
- [x] Создать документацию по архитектуре
- [x] Создать гибридный подход к управлению состоянием
- [x] Создать отдельный контекст для управления темой
- [x] Объединить компоненты 'Найди ноту' и 'Найди интервал'
- [x] Мигрировать компонент Name The Chord
- [x] Мигрировать компонент Chord Trainer
