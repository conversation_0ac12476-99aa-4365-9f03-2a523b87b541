# Технический Стек Проекта "Guitar Club Web App"

Этот документ описывает основные технологии, фреймворки и библиотеки, используемые в разработке веб-приложения Guitar Club.

## Основные Технологии

* **[React](https://reactjs.org/) (v19.1.0)**: Библиотека JavaScript для построения пользовательских интерфейсов. Используется для создания интерактивных компонентов приложения (тренажеры, элементы управления, навигация). Применяется подход с функциональными компонентами и хуками (Hooks).

* **[TypeScript](https://www.typescriptlang.org/) (v5.8.3)**: Строго типизированный язык программирования, компилируемый в JavaScript. Используется для повышения надежности кода, улучшения опыта разработки (автодополнение, ранняя ловля ошибок) и упрощения поддержки проекта.

* **[Vite](https://vitejs.dev/) (v6.3.3)**: Современный инструмент для сборки фронтенда и сервер разработки. Обеспечивает чрезвычайно быструю горячую замену модулей (HMR) во время разработки и оптимизированные сборки для продакшена.

* **[Web Audio API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API)**: Низкоуровневый API браузера для обработки и синтеза звука. Является основой для реализации функционала:
    * Гитарного тюнера (доступ к микрофону, анализ частоты).
    * Метронома (генерация точных по времени кликов).
    * Тренажера относительного слуха (генерация и воспроизведение нот/интервалов).

* **[SVG (Scalable Vector Graphics)](https://developer.mozilla.org/en-US/docs/Web/SVG)**: Формат для описания двумерной векторной графики. Используется для отрисовки:
    * Интерактивного гитарного грифа.
    * Аккордовых диаграмм.
    * Элементов визуализации тюнера.
    Интеграция с React позволяет динамически изменять SVG на основе состояния приложения.

* **[Tailwind CSS](https://tailwindcss.com/) (v4.1.4)**: Utility-first CSS фреймворк. Используется для быстрой и консистентной стилизации компонентов и создания адаптивного дизайна без написания большого количества кастомного CSS. Версия 4 предлагает улучшенную производительность, новые возможности и более современный синтаксис.

* **[PWA (Progressive Web App)](https://web.dev/progressive-web-apps/)**: Набор технологий (включая Service Workers и Web App Manifest), используемых для придания веб-приложению возможностей, схожих с нативными:
    * Установка на устройство пользователя.
    * Работа в оффлайн-режиме (для части функционала).
    Настройка осуществляется с использованием возможностей Vite (например, через плагин `vite-plugin-pwa`).

## Библиотеки и Компоненты

### UI Компоненты

* **[shadcn/ui](https://ui.shadcn.com/)**: Коллекция переиспользуемых компонентов, построенных с использованием Radix UI и Tailwind CSS. Не является библиотекой в традиционном смысле, а скорее набором компонентов, которые можно копировать и настраивать под нужды проекта.

* **[Radix UI](https://www.radix-ui.com/)**: Низкоуровневая библиотека UI примитивов, которая используется как основа для компонентов shadcn/ui. Обеспечивает доступность и интерактивность компонентов.

### Иконки и Визуальные Элементы

* **[lucide-react](https://lucide.dev/) (v0.503.0)**: Библиотека иконок для React-приложений. Используется для отображения иконок в интерфейсе.

* **[react-icons](https://react-icons.github.io/react-icons/) (v5.5.0)**: Библиотека иконок для React-приложений. Дополняет lucide-react для более широкого выбора иконок.

### Маршрутизация и Навигация

* **[react-router-dom](https://reactrouter.com/) (v7.5.1)**: Библиотека для маршрутизации в React-приложениях. Используется для навигации между различными страницами приложения.

### Визуализация Данных

* **[recharts](https://recharts.org/) (v2.15.3)**: Библиотека для создания графиков и диаграмм в React-приложениях. Используется для визуализации статистики.

### Аудио и Анализ Звука

* **[pitchy](https://www.npmjs.com/package/pitchy) (v4.1.0)**: Библиотека для анализа высоты звука. Используется в тюнере и тренажере относительного слуха.

## Инструменты Разработки

### Система Контроля Версий

* **Git**: Система контроля версий для отслеживания изменений в коде и совместной работы.

### Среда Выполнения и Управление Пакетами

* **Node.js**: Среда выполнения JavaScript на стороне сервера.
* **npm**: Менеджер пакетов для установки и управления зависимостями проекта.

### Линтинг и Форматирование

* **ESLint**: Инструмент для статического анализа кода и выявления проблем.
* **Prettier**: Инструмент для автоматического форматирования кода.

## Обоснование Выбора Технологий

### React и TypeScript

Комбинация React и TypeScript была выбрана из-за:
- Широкого распространения и поддержки сообщества
- Компонентного подхода, который идеально подходит для создания модульных тренажеров
- Строгой типизации, которая снижает количество ошибок во время разработки
- Хорошей производительности и оптимизации рендеринга

### Vite

Vite был выбран вместо Create React App или других инструментов сборки из-за:
- Значительно более быстрой разработки благодаря мгновенному HMR
- Оптимизированной сборки для продакшена
- Встроенной поддержки TypeScript и CSS-препроцессоров
- Простой конфигурации и расширяемости через плагины

### Tailwind CSS и shadcn/ui

Эта комбинация была выбрана для стилизации из-за:
- Utility-first подхода, который ускоряет разработку UI
- Консистентности дизайна благодаря предопределенным значениям
- Хорошей поддержки темной темы и адаптивного дизайна
- Возможности создания кастомных компонентов на основе готовых примитивов

## Запуск Проекта

```bash
# Переход в директорию проекта
cd guitar-app

# Установка зависимостей
npm install

# Запуск в режиме разработки
npm start

# Сборка для продакшена
npm run build
```