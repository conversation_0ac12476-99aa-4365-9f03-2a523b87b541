# Руководство по UI

Краткое руководство по дизайну и UI компонентам проекта.

## Основные технологии для UI

- **React** (v19+) с функциональными компонентами и хуками
- **Tailwind CSS** (v4+) для стилизации
- **shadcn/ui** с использованием стиля New York и нейтральной цветовой схемы
- **lucide-react** (v0.503.0+) и **react-icons** (v5.5.0+) для иконок
- **SVG** для интерактивных элементов (гриф, диаграммы)

## Стилизация с Tailwind CSS

- **Подход**: Utility-First. Применяйте стили через классы Tailwind в JSX
- **Цвета**: Используйте только цвета из `tailwind.config.js` (`bg-primary`, `text-accent`)
- **Адаптивность**: Используйте префиксы `sm:`, `md:`, `lg:` для адаптивного дизайна
- **Темная тема**: Поддерживайте темную тему через классы `dark:*`

```tsx
// Пример использования Tailwind CSS
<div className="flex items-center justify-between p-4 bg-card dark:bg-card-dark rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-foreground">Заголовок</h2>
  <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
    Кнопка
  </button>
</div>
```

## Компоненты shadcn/ui

Все компоненты shadcn/ui находятся в директории `src/components/ui`.

### Основные компоненты

- **Button** (`button.tsx`) - для всех кнопок в приложении
- **Card** (`card.tsx`) - для карточек и контейнеров
- **Select** (`select.tsx`) - для выпадающих списков
- **Dialog** (`dialog.tsx`) - для модальных окон
- **Switch** (`switch.tsx`) - для переключателей (вместо чекбоксов)
- **Slider** (`slider.tsx`) - для ползунков
- **Tooltip** (`tooltip.tsx`) - для всплывающих подсказок

### Примеры использования

```tsx
// Кнопка
<Button variant="default">Начать</Button>
<Button variant="destructive">Стоп</Button>
<Button variant="outline">Настройки</Button>

// Карточка
<Card>
  <CardHeader>
    <CardTitle>Тренажер аккордов</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Содержимое карточки</p>
  </CardContent>
  <CardFooter>
    <Button>Начать</Button>
  </CardFooter>
</Card>

// Диалоговое окно
<Dialog>
  <DialogTrigger asChild>
    <Button>Открыть</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Настройки</DialogTitle>
    </DialogHeader>
    <div className="py-4">Содержимое</div>
  </DialogContent>
</Dialog>
```

## Принципы дизайна

1. **Стиль New York**: Более строгие, прямоугольные формы с минимальными скруглениями
2. **Нейтральная цветовая схема**: Хорошо работает в светлой и темной теме
3. **Простота и ясность**: Чистые, интуитивно понятные интерфейсы без визуального шума
4. **Консистентность**: Единообразие в отступах, шрифтах, цветах, стилях кнопок
5. **Иерархия**: Четкая визуальная иерархия через размеры шрифтов и отступы
6. **Отзывчивость**: Визуальная обратная связь на действия пользователя
7. **Адаптивность**: Корректное отображение на устройствах разных размеров

## Иконки и графика

- **Основная библиотека иконок**: `lucide-react`
- **Дополнительная библиотека**: `react-icons` (только если нужной иконки нет в lucide)
- **Размеры иконок**: `h-4 w-4` (маленькие), `h-5 w-5` (средние), `h-6 w-6` (большие)
- **SVG**: Используйте для интерактивных диаграмм, грифа и т.д.

## Цветовая схема

- **Основные действия**: `default` (основной), `destructive` (красный), `success` (зеленый)
- **Состояния**: `text-green-500` (успех), `text-red-500` (ошибка), `text-yellow-500` (предупреждение)
- **Фон**: `bg-background` (основной), `bg-card` (карточки), `bg-muted` (приглушенный)
- **Текст**: `text-foreground` (основной), `text-muted-foreground` (второстепенный)

## Специфические UI компоненты

### Гитарный гриф

- Используйте SVG для отрисовки грифа
- Отображайте ноты как круги на пересечении струн и ладов
- Выделяйте активные ноты цветом (`bg-primary`)
- Отображайте корневые ноты как квадраты с закругленными углами

### Аккордовые диаграммы

- Используйте SVG для отрисовки диаграмм
- Отображайте аппликатуру аккорда с указанием пальцев
- Используйте консистентные обозначения для открытых и закрытых струн

### Статистика

- Отображайте статистику в одну строку: Счет, Точность, Скорость, Время
- Используйте иконки для визуального различия показателей
- Для скорости используйте "акк/мин" в "Name The Chord"
- Отображайте числовые значения без десятичных знаков

## Лучшие практики UI

1. **Предотвращение прыжков интерфейса**
   - Фиксируйте высоту контейнеров, где могут меняться значения
   - Используйте `min-height` для элементов с динамическим содержимым

2. **Обратная связь**
   - Добавляйте hover-эффекты для интерактивных элементов
   - Используйте анимации для переходов между состояниями
   - Добавляйте визуальную индикацию для правильных/неправильных ответов

3. **Доступность**
   - Используйте достаточный контраст между текстом и фоном
   - Добавляйте подписи или всплывающие подсказки к иконкам
   - Обеспечивайте возможность навигации с клавиатуры

4. **Мобильная адаптация**
   - Увеличивайте размер интерактивных элементов на мобильных устройствах
   - Адаптируйте расположение элементов для вертикального отображения
   - Тестируйте на различных размерах экрана
