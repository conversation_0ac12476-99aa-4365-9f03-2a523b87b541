# Руководство по Системе Дизайна: Guitar Club Web App

Этот документ содержит основные правила и предпочтения для UI-компонентов и дизайна экосистемы музыкальных приложений "Guitar Club". Следуйте этим инструкциям для обеспечения консистентности и качества.

## 1. Основной Стек Технологий для UI

* **Фреймворк/Библиотека:** React (v19+) с использованием функциональных компонентов и хуков.
* **Язык:** TypeScript.
* **Стилизация:** Tailwind CSS (v4+).
* **Компоненты:** shadcn/ui с использованием стиля New York и нейтральной цветовой схемы.
* **Иконки:** lucide-react (v0.503.0+) и react-icons (v5.5.0+).
* **Графика:** SVG для интерактивных элементов (гриф, диаграммы).

## 2. Стилизация: Tailwind CSS

* **Основной инструмент:** Tailwind CSS (v4+).
* **Подход:** **Utility-First.** Применяйте стили непосредственно через классы Tailwind в JSX. Избегайте написания кастомного CSS, кроме случаев крайней необходимости.
* **Конфигурация (`tailwind.config.js`):**
    * **Используйте предопределенные значения:** Отдавайте предпочтение стандартным значениям Tailwind для отступов, размеров шрифтов, цветов и т.д.
    * **Палитра:** Используйте *только* цвета, определенные в `tailwind.config.js` (например, `bg-primary`, `text-accent`, `border-neutral`).
    * **Шрифты:** Используйте шрифты, определенные в конфигурации.
* **Адаптивность:** Обязательно используйте адаптивные префиксы Tailwind (`sm:`, `md:`, `lg:`) для создания полностью адаптивных интерфейсов.

## 3. Компоненты UI: shadcn/ui

* **Основная библиотека компонентов:** shadcn/ui с использованием стиля New York и нейтральной цветовой схемы.
* **Расположение компонентов:** Все компоненты shadcn/ui должны находиться в директории `src/components/ui`.
* **Используемые компоненты:**
    * **Button** (`button.tsx`) - для всех кнопок в приложении
    * **Card, CardContent** (`card.tsx`) - для карточек и контейнеров
    * **Select, SelectContent, SelectItem, SelectTrigger, SelectValue** (`select.tsx`) - для выпадающих списков
    * **Dialog, DialogHeader, DialogTitle, DialogContent** (`dialog.tsx`) - для модальных окон
    * **Badge** (`badge.tsx`) - для отображения статусов и меток
    * **Switch** (`switch.tsx`) - для переключателей (вместо чекбоксов)
    * **Slider** (`slider.tsx`) - для ползунков
    * **ScrollArea** (`scroll-area.tsx`) - для областей с прокруткой
    * **Separator** (`separator.tsx`) - для разделителей
    * **Tooltip** (`tooltip.tsx`) - для всплывающих подсказок

## 4. Принципы Дизайна и UX

* **Стиль shadcn/ui:** Следуйте стилю New York из библиотеки shadcn/ui
* **Цветовая схема:** Используйте нейтральную цветовую схему, которая хорошо работает как в светлой, так и в темной теме.
* **Простота и Ясность:** Создавайте чистые, интуитивно понятные интерфейсы. Избегайте визуального шума.
* **Консистентность:** Строго придерживайтесь единообразия в отступах, шрифтах, цветах, стилях кнопок, форм и других элементов во всей экосистеме.
* **Иерархия:** Используйте размеры шрифтов, насыщенность и отступы для создания четкой визуальной иерархии на странице.
* **Отступы (Whitespace):** Активно используйте отступы для улучшения читаемости и организации контента.
* **Обратная связь:** Предоставляйте пользователю четкую визуальную обратную связь на его действия.
* **Доступность (Accessibility):** Используйте семантически верные HTML-теги и атрибуты ARIA, которые уже встроены в компоненты shadcn/ui.
* **Адаптивность:** Все компоненты должны корректно отображаться на устройствах разных размеров.
* **Темы:** Поддерживайте как светлую, так и темную тему с автоматическим переключением в зависимости от системных настроек и возможностью ручного переключения.

## 5. Ассеты (Иконки и Графика)

* **Иконки:**
    * **Основная библиотека:** **`lucide-react`** (версия 0.503.0+). Используйте ее в первую очередь для обеспечения консистентного стиля.
    * **Дополнительная библиотека:** `react-icons` (версия 5.5.0+) может использоваться, если нужная иконка отсутствует в `lucide-react`.
    * **Размеры иконок:** Используйте стандартные размеры - `h-4 w-4` для маленьких иконок (в кнопках), `h-5 w-5` для средних и `h-6 w-6` для больших.
    * **Стилизация:** Иконки должны соответствовать общей цветовой палитре и размерной сетке.
* **SVG:**
    * Используйте для интерактивных диаграмм, грифа и т.д.
    * Стилизуйте SVG с использованием классов Tailwind там, где это возможно.
    * Обеспечьте интерактивность (hover-эффекты, кликабельность) для соответствующих элементов SVG.
* **Цветовая схема:**
    * Следуйте нейтральной цветовой схеме shadcn/ui.
    * Для кнопок действий используйте стандартные варианты: `default` (основной), `destructive` (красный для удаления/остановки), `success` (зеленый для подтверждения/запуска).
    * Для состояний используйте соответствующие цвета: `text-green-500` для успеха, `text-red-500` для ошибок, `text-yellow-500` для предупреждений.

## 6. Миграция на shadcn/ui

### Стратегия миграции

Проект находится в процессе миграции с собственных UI компонентов на компоненты из библиотеки shadcn/ui:

1. **Создание новых версий компонентов:**
   - Новые версии создаются с суффиксом "New" (например, `NameTheChordNew.tsx`)
   - Функциональность сохраняется, меняется только UI-слой

2. **Сохранение обратной совместимости:**
   - Старые версии компонентов сохраняются в проекте
   - Доступ к старым версиям осуществляется через альтернативные маршруты

3. **Обновление маршрутизации:**
   - Основные маршруты в `App.tsx` обновляются для использования новых компонентов
   - Добавляются альтернативные маршруты для доступа к старым версиям (с суффиксом "-old")

### Приоритеты миграции

- Начинайте с наиболее часто используемых компонентов
- Сохраняйте функциональность при миграции, фокусируясь только на UI-изменениях
- Стандартизируйте стили между компонентами, следуя стилю New York из shadcn/ui

### Тестирование

- Проверяйте работу компонентов после миграции на различных устройствах и в разных темах
- Убедитесь, что все функции работают так же, как в оригинальных компонентах

### Документация

- Обновляйте документацию по мере миграции компонентов
- Отмечайте компоненты, которые уже мигрированы на shadcn/ui

## 7. Статус миграции компонентов

| Компонент | Статус | Новая версия | Комментарии |
|-----------|--------|--------------|-------------|
| Name The Chord | ✅ Мигрирован | NameTheChordNew.tsx | Полностью переведен на shadcn/ui |
| Chord Trainer | ✅ Мигрирован | ChordTrainerNew.tsx | Полностью переведен на shadcn/ui |
| Guitar Note/Interval Game | ⏳ В процессе | - | Планируется миграция |
| Pitch Trainer | ⏳ В процессе | - | Планируется миграция |
| Note Recognition | ⏳ В процессе | - | Планируется миграция |
| Guitar Neck Editor | ⏳ В процессе | - | Планируется миграция |
| Score Counter | ⏳ В процессе | - | Планируется миграция |
| Random Chord Generator | ⏳ В процессе | - | Планируется миграция |
| Home Page | ✅ Использует Tailwind | - | Не требует полной миграции |

## 8. Примеры компонентов

### Кнопки

```tsx
// Основная кнопка
<Button>Начать</Button>

// Кнопка с иконкой
<Button>
  <PlayIcon className="mr-2 h-4 w-4" />
  Начать
</Button>

// Деструктивная кнопка (для остановки, удаления)
<Button variant="destructive">Стоп</Button>

// Кнопка успеха (для подтверждения, запуска)
<Button variant="success">Начать</Button>

// Контурная кнопка
<Button variant="outline">Настройки</Button>

// Кнопка-ссылка
<Button variant="link">Подробнее</Button>
```

### Карточки

```tsx
<Card>
  <CardHeader>
    <CardTitle>Тренажер аккордов</CardTitle>
    <CardDescription>Изучайте аккорды на гитаре</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Содержимое карточки</p>
  </CardContent>
  <CardFooter>
    <Button>Начать</Button>
  </CardFooter>
</Card>
```

### Диалоговые окна

```tsx
<Dialog>
  <DialogTrigger asChild>
    <Button variant="outline">Настройки</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Настройки</DialogTitle>
    </DialogHeader>
    <div className="grid gap-4 py-4">
      {/* Содержимое диалога */}
    </div>
    <DialogFooter>
      <Button>Сохранить</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Переключатели

```tsx
<div className="flex items-center space-x-2">
  <Switch id="airplane-mode" />
  <Label htmlFor="airplane-mode">Режим микрофона</Label>
</div>
```

### Выпадающие списки

```tsx
<Select>
  <SelectTrigger className="w-[180px]">
    <SelectValue placeholder="Выберите аккорд" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="c-major">C Major</SelectItem>
    <SelectItem value="g-major">G Major</SelectItem>
    <SelectItem value="a-minor">A Minor</SelectItem>
  </SelectContent>
</Select>
```
