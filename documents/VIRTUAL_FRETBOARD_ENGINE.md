# Схема Движка Виртуального Гитарного Грифа

Этот документ описывает концептуальную схему и основные компоненты движка для виртуального гитарного грифа, используемого в приложениях Guitar Club.

## 1. Основные Компоненты

Движок состоит из нескольких взаимосвязанных модулей:

*   **MIDI-ядро (MIDI Core):** Отвечает за генерацию и отправку MIDI-сообщений (Note On, Note Off) при взаимодействии пользователя с грифом.
*   **Модуль Строя (Tuning Module):** Определяет строй гитары (стандартный EADGBE, Drop D, Open G и т.д.). Хранит MIDI-номера для каждой открытой струны.
*   **Модуль Отображения Грифа (Fretboard Display Module):** Визуализирует гриф, струны, лады и позиции нот. Отвечает за подсветку нот, интервалов, аккордов и гамм.
*   **Модуль Логики Позиций (Position Logic Module):** Рассчитывает MIDI-номер ноты для любой позиции на грифе (струна + лад) на основе выбранного строя.
*   **Модуль Взаимодействия с Синтезатором (Synth Interaction Module):** Передает MIDI-сообщения от MIDI-ядра к внешнему или встроенному синтезатору (например, через Web MIDI API или Tone.js).
*   **Модуль Теории Музыки (Music Theory Module):** Хранит данные об аккордах, гаммах, ладах, тональностях и других теоретических концепциях. Предоставляет эту информацию другим модулям, в первую очередь Модулю Отображения Грифа.

## 2. Процесс Работы

1.  **Инициализация:** Задается строй гитары (по умолчанию стандартный).
2.  **Взаимодействие:** Пользователь кликает/касается определенной позиции на виртуальном грифе (например, 5-й лад на 3-й струне).
3.  **Расчет Ноты:** Модуль Логики Позиций определяет MIDI-номер ноты для этой позиции (для стандартного строя G3 + 5 полутонов = C4, MIDI 60).
4.  **Генерация MIDI:** MIDI-ядро генерирует сообщение `Note On` (например, `[144, 60, 100]` - Note On, канал 1, нота C4, velocity 100).
5.  **Отправка Синтезатору:** Модуль Взаимодействия передает MIDI-сообщение синтезатору.
6.  **Воспроизведение Звука:** Синтезатор воспроизводит соответствующую ноту.
7.  **Отпускание Ноты:** Когда пользователь отпускает позицию, генерируется `Note Off` (`[128, 60, 0]`).

## 3. Обработка Дублирования Нот

Одна и та же нота (например, E4) может встречаться в нескольких позициях на грифе (открытая 1-я струна, 5-й лад 2-й струны, 9-й лад 3-й струны и т.д.).

*   **Отображение:** Модуль Отображения может подсвечивать *все* позиции, соответствующие определенной ноте или набору нот (например, для аккорда). Он также использует данные из **Модуля Теории Музыки** для визуализации аппликатур аккордов, гамм, интервалов и других теоретических структур на грифе.
*   **Воспроизведение:** При клике на конкретную позицию воспроизводится именно эта нота. Движок не пытается "угадать", какую из дублирующих позиций пользователь имел в виду.

## 4. Диаграмма взаимодействия модулей

```mermaid
graph LR
    A[Пользователь] --> B(Модуль Отображения Грифа);
    B -- Запрос позиции --> C(Модуль Логики Позиций);
    D(Модуль Строя) --> C;
    C -- MIDI-нота --> E(MIDI-ядро);
    E -- MIDI-сообщение --> F(Модуль Взаимодействия с Синтезатором);
    F --> G[Синтезатор];
    H(Модуль Теории Музыки) --> B;
    H --> C;
```

**Описание взаимодействия:**

1.  **Пользователь** взаимодействует с **Модулем Отображения Грифа** (кликает/касается).
2.  **Модуль Отображения Грифа** запрашивает у **Модуля Логики Позиций** MIDI-ноту для выбранной позиции.
3.  **Модуль Логики Позиций** использует данные из **Модуля Строя** для расчета MIDI-ноты.
4.  Рассчитанная MIDI-нота передается в **MIDI-ядро**.
5.  **MIDI-ядро** генерирует MIDI-сообщение (Note On/Off).
6.  **Модуль Взаимодействия с Синтезатором** отправляет MIDI-сообщение **Синтезатору**.
7.  **Синтезатор** воспроизводит звук.
8.  **Модуль Теории Музыки** предоставляет данные (аккорды, гаммы) **Модулю Отображения Грифа** для визуализации и **Модулю Логики Позиций** (например, для определения нот аккорда).